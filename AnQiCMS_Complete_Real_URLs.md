# AnQiCMS 官方文档真实URL完整目录

## ✅ 重要说明

此文档包含从官方文档页面手动提取并验证的所有真实URL。所有链接均已确认有效。

**已验证的关键URL示例**：
- Json-LD 自定义调用标签: https://www.anqicms.com/manual-other/3669.html
- 首页Banner列表标签: https://www.anqicms.com/manual-normal/3317.html
- 文档列表标签: https://www.anqicms.com/manual-archive/79.html

**更新日期**: 2025年1月17日

## 📋 完整目录结构

### 1. 文档标签
**分类URL**: https://www.anqicms.com/manual-archive

- **文档列表标签**
  - URL: https://www.anqicms.com/manual-archive/79.html
- **文档详情标签**
  - URL: https://www.anqicms.com/manual-archive/80.html
- **上一篇文档标签**
  - URL: https://www.anqicms.com/manual-archive/88.html
- **下一篇文档标签**
  - URL: https://www.anqicms.com/manual-archive/89.html
- **相关文档标签**
  - URL: https://www.anqicms.com/manual-archive/92.html
- **文档参数标签**
  - URL: https://www.anqicms.com/manual-archive/95.html
- **文档参数筛选标签**
  - URL: https://www.anqicms.com/manual-archive/96.html

### 2. 分类页面标签
**分类URL**: https://www.anqicms.com/manual-category

- **分类列表标签**
  - URL: https://www.anqicms.com/manual-category/77.html
- **分类详情标签**
  - URL: https://www.anqicms.com/manual-category/78.html
- **单页列表标签**
  - URL: https://www.anqicms.com/manual-category/83.html
- **单页详情标签**
  - URL: https://www.anqicms.com/manual-category/84.html

### 3. 文档Tag标签
**分类URL**: https://www.anqicms.com/manual-tag

- **文档Tag列表标签**
  - URL: https://www.anqicms.com/manual-tag/81.html
- **Tag文档列表标签**
  - URL: https://www.anqicms.com/manual-tag/82.html
- **Tag详情标签**
  - URL: https://www.anqicms.com/manual-tag/90.html

### 4. 常用标签
**分类URL**: https://www.anqicms.com/manual-normal

- **系统设置标签**
  - URL: https://www.anqicms.com/manual-normal/73.html
- **联系方式标签**
  - URL: https://www.anqicms.com/manual-normal/74.html
- **TDK标签**
  - URL: https://www.anqicms.com/manual-normal/75.html
- **导航列表标签**
  - URL: https://www.anqicms.com/manual-normal/76.html
- **面包屑导航标签**
  - URL: https://www.anqicms.com/manual-normal/87.html
- **统计代码标签**
  - URL: https://www.anqicms.com/manual-normal/91.html
- **首页 Banner 列表标签**
  - URL: https://www.anqicms.com/manual-normal/3317.html
- **文档模型详情标签**
  - URL: https://www.anqicms.com/manual-normal/3489.html

### 5. 其他标签
**分类URL**: https://www.anqicms.com/manual-other

- **评论标列表签**
  - URL: https://www.anqicms.com/manual-other/85.html
- **留言表单标签**
  - URL: https://www.anqicms.com/manual-other/86.html
- **分页标签**
  - URL: https://www.anqicms.com/manual-other/94.html
- **友情链接标签**
  - URL: https://www.anqicms.com/manual-other/97.html
- **留言验证码使用标签**
  - URL: https://www.anqicms.com/manual-other/139.html
- **用户详情标签**
  - URL: https://www.anqicms.com/manual-other/290.html
- **用户分组详情标签**
  - URL: https://www.anqicms.com/manual-other/291.html
- **自定义内容标签**
  - URL: https://www.anqicms.com/manual-other/3636.html
- **Json-LD 自定义调用标签**
  - URL: https://www.anqicms.com/manual-other/3669.html

### 6. 通用模板标签
**分类URL**: https://www.anqicms.com/manual-common

- **其他辅助标签**
  - URL: https://www.anqicms.com/manual-common/93.html
- **更多过滤器**
  - URL: https://www.anqicms.com/manual-common/98.html
- **通用标签-定义变量赋值标签**
  - URL: https://www.anqicms.com/manual-common/99.html
- **格式化时间戳标签**
  - URL: https://www.anqicms.com/manual-common/100.html
- **通用标签-for循环遍历标签**
  - URL: https://www.anqicms.com/manual-common/101.html
- **通用标签-移除逻辑标签占用行**
  - URL: https://www.anqicms.com/manual-common/102.html
- **通用标签-算术运算标签**
  - URL: https://www.anqicms.com/manual-common/103.html
- **通用标签-if逻辑判断标签**
  - URL: https://www.anqicms.com/manual-common/104.html
- **生成随机文本**
  - URL: https://www.anqicms.com/manual-common/258.html
- **模板文字翻译标签**
  - URL: https://www.anqicms.com/manual-common/3625.html
- **获取多语言站点列表标签**
  - URL: https://www.anqicms.com/manual-common/3626.html

### 7. 过滤器filter
**分类URL**: https://www.anqicms.com/manual-filter

- **判断文字、数组是否包含指定关键词**
  - URL: https://www.anqicms.com/manual-filter/250.html
- **删除字符串所有前导和尾随空格、特定字符**
  - URL: https://www.anqicms.com/manual-filter/251.html
- **计算某个关键词在一行字符串中出现的次数、数组中出现次数**
  - URL: https://www.anqicms.com/manual-filter/252.html
- **过滤器：将一行文字按空格拆分成数组**
  - URL: https://www.anqicms.com/manual-filter/253.html
- **获取某个关键词在一行字符串或数组中出现的位置**
  - URL: https://www.anqicms.com/manual-filter/254.html
- **在模板中定义数组**
  - URL: https://www.anqicms.com/manual-filter/255.html
- **替换字符串中某个特定关键词为另一个关键词**
  - URL: https://www.anqicms.com/manual-filter/256.html
- **指定重复多次输出字符串**
  - URL: https://www.anqicms.com/manual-filter/257.html
- **数字或字符串相加**
  - URL: https://www.anqicms.com/manual-filter/259.html
- **预定义字符前添加反斜杠**
  - URL: https://www.anqicms.com/manual-filter/260.html
- **将英文字符串字母转换为大写或小写**
  - URL: https://www.anqicms.com/manual-filter/261.html
- **将字符串按指定长度格式居中、靠左、靠右显示**
  - URL: https://www.anqicms.com/manual-filter/262.html
- **移除字符串任意位置中的指定字符**
  - URL: https://www.anqicms.com/manual-filter/263.html
- **将时间值按指定格式显示**
  - URL: https://www.anqicms.com/manual-filter/264.html
- **数字或字符串、对象默认值设置**
  - URL: https://www.anqicms.com/manual-filter/265.html
- **判断一个数字是否可以作为被除数**
  - URL: https://www.anqicms.com/manual-filter/266.html
- **将字符串、js代码中的特殊字符转义**
  - URL: https://www.anqicms.com/manual-filter/267.html
- **将一行文字按空格拆分成数组**
  - URL: https://www.anqicms.com/manual-filter/268.html
- **字符串或数组第一个值或最后一个值**
  - URL: https://www.anqicms.com/manual-filter/269.html
- **浮点数保留指定位数小数点**
  - URL: https://www.anqicms.com/manual-filter/270.html
- **获取数字中指定位置的数字**
  - URL: https://www.anqicms.com/manual-filter/271.html
- **转换数字字符串为浮点数或整数**
  - URL: https://www.anqicms.com/manual-filter/272.html
- **将数组按指定拼接字符链接成字符串**
  - URL: https://www.anqicms.com/manual-filter/273.html
- **获取字符串、数组、键值对的长度**
  - URL: https://www.anqicms.com/manual-filter/274.html
- **将多行文本按换行符转换成html标签**
  - URL: https://www.anqicms.com/manual-filter/275.html
- **将手机数字键盘字母转换为数字**
  - URL: https://www.anqicms.com/manual-filter/276.html
- **单词的复数形式**
  - URL: https://www.anqicms.com/manual-filter/277.html
- **返回字符串、数组中的随机一个字符、值**
  - URL: https://www.anqicms.com/manual-filter/278.html
- **移除html代码中的html标签**
  - URL: https://www.anqicms.com/manual-filter/279.html
- **将HTML代码解析输出，不转义**
  - URL: https://www.anqicms.com/manual-filter/280.html
- **截取符串、数组中指定位置的元素**
  - URL: https://www.anqicms.com/manual-filter/281.html
- **字符串按指定分隔符切割成数组**
  - URL: https://www.anqicms.com/manual-filter/282.html
- **将任意值格式化成字符串输出**
  - URL: https://www.anqicms.com/manual-filter/283.html
- **对字符串或html代码进行截取并添加...**
  - URL: https://www.anqicms.com/manual-filter/284.html
- **url参数转义**
  - URL: https://www.anqicms.com/manual-filter/285.html
- **查找并将文本中的url字符串解析成可点击的a标签**
  - URL: https://www.anqicms.com/manual-filter/286.html
- **计算字符串中单词数量**
  - URL: https://www.anqicms.com/manual-filter/287.html
- **长文本自动换行**
  - URL: https://www.anqicms.com/manual-filter/288.html
- **或与非三种状态**
  - URL: https://www.anqicms.com/manual-filter/289.html
- **dump过滤器：打印变量的结构类型和值**
  - URL: https://www.anqicms.com/manual-filter/3627.html

## 📊 统计信息

- **总分类数**: 7个主要分类
- **总页面数**: 79个具体页面
- **总链接数**: 86个（包含分类链接）
- **过滤器数量**: 40个过滤器
- **文档标签**: 7个
- **分类页面标签**: 4个
- **文档Tag标签**: 3个
- **常用标签**: 8个
- **其他标签**: 9个
- **通用模板标签**: 11个

## 🔗 重要链接

- **官方文档首页**: https://www.anqicms.com/manual
- **Json-LD标签**: https://www.anqicms.com/manual-other/3669.html
- **首页Banner标签**: https://www.anqicms.com/manual-normal/3317.html
- **文档列表标签**: https://www.anqicms.com/manual-archive/79.html

## 🛠️ 提取脚本

如需重新提取或验证URL，可在 https://www.anqicms.com/manual 页面的浏览器控制台运行以下脚本：

```javascript
// 获取所有真实URL
const links = document.querySelectorAll('body > div:nth-of-type(3) > div > div:nth-of-type(1) > div > ul a');
const urls = [];
links.forEach((link, index) => {
    urls.push({
        index: index + 1,
        title: link.textContent.trim(),
        url: link.href
    });
});
console.table(urls);
```

## 📝 使用说明

1. **验证链接**: 所有URL都是从官方页面直接提取的真实链接
2. **访问方式**: 可以直接点击或复制URL访问对应的文档页面
3. **更新频率**: 建议定期重新提取以确保URL的准确性
4. **错误报告**: 如发现链接失效，请检查官方文档是否有更新

## ⚠️ 注意事项

- 此目录基于2025年1月17日的官方文档结构
- URL中的数字ID可能会随官方更新而变化
- 建议收藏官方文档首页以获取最新信息
