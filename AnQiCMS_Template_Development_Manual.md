# AnQiCMS 模板开发手册

## 目录

1. [模板制作基础](#模板制作基础)
   - [模板制作的基本约定](#模板制作的基本约定)
   - [模板目录结构和配置](#模板目录结构和配置)
   - [模板标签概览](#模板标签概览)

2. [文档标签相关](#文档标签相关)
   - [文档列表标签](#文档列表标签)
   - [文档详情标签](#文档详情标签)
   - [上一篇文档标签](#上一篇文档标签)
   - [下一篇文档标签](#下一篇文档标签)
   - [相关文档标签](#相关文档标签)
   - [文档参数标签](#文档参数标签)
   - [文档参数筛选标签](#文档参数筛选标签)

3. [分类页面标签相关](#分类页面标签相关)
   - [分类列表标签](#分类列表标签)
   - [分类详情标签](#分类详情标签)
   - [单页列表标签](#单页列表标签)
   - [单页详情标签](#单页详情标签)

4. [文档Tag标签相关](#文档tag标签相关)
   - [文档Tag列表标签](#文档tag列表标签)
   - [Tag文档列表标签](#tag文档列表标签)
   - [Tag详情标签](#tag详情标签)

5. [常用标签相关](#常用标签相关)
   - [系统设置标签](#系统设置标签)
   - [联系方式标签](#联系方式标签)
   - [TDK标签](#tdk标签)
   - [导航列表标签](#导航列表标签)
   - [面包屑导航标签](#面包屑导航标签)
   - [统计代码标签](#统计代码标签)
   - [首页Banner列表标签](#首页banner列表标签)
   - [文档模型详情标签](#文档模型详情标签)

6. [其他标签相关](#其他标签相关)
   - [评论标列表签](#评论标列表签)
   - [留言表单标签](#留言表单标签)
   - [分页标签](#分页标签)
   - [友情链接标签](#友情链接标签)
   - [留言验证码使用标签](#留言验证码使用标签)
   - [用户详情标签](#用户详情标签)
   - [用户分组详情标签](#用户分组详情标签)
   - [自定义内容标签](#自定义内容标签)
   - [Json-LD自定义调用标签](#json-ld自定义调用标签)

7. [通用模板标签相关](#通用模板标签相关)
   - [其他辅助标签](#其他辅助标签)
   - [更多过滤器](#更多过滤器)
   - [通用标签-定义变量赋值标签](#通用标签-定义变量赋值标签)
   - [格式化时间戳标签](#格式化时间戳标签)
   - [通用标签-for循环遍历标签](#通用标签-for循环遍历标签)
   - [通用标签-移除逻辑标签占用行](#通用标签-移除逻辑标签占用行)
   - [通用标签-算术运算标签](#通用标签-算术运算标签)
   - [通用标签-if逻辑判断标签](#通用标签-if逻辑判断标签)
   - [生成随机文本](#生成随机文本)
   - [模板文字翻译标签](#模板文字翻译标签)
   - [获取多语言站点列表标签](#获取多语言站点列表标签)

8. [过滤器filter相关](#过滤器filter相关)
   - [完整过滤器列表](#完整过滤器列表)
   - [过滤器链式使用](#过滤器链式使用)

9. [高级功能和技巧](#高级功能和技巧)

10. [常见问题和解决方案](#常见问题和解决方案)

11. [总结](#总结)

---

## 模板制作基础

### 模板制作的基本约定

AnqiCMS 的模板，使用 `.html` 作为模板文件后缀，并存放在 `/template` 模板文件夹中，模板用到的样式、js脚本、图片等静态资源，则单独存放在 `/public/static/` 目录。

#### 模板语法规则

**变量语法：** 使用双花括号来定义变量，如 `{{变量}}`

**标签语法：** 条件判断、循环控制等标签，使用单花括号和百分号来定义，并且需要用结束标签来结束，标签成对出现，如：
```html
{% if archive.Id == 10 %}
这是文档ID为10的文档
{% endif %}
```

**命名规则：** 变量名采用驼峰命名法则，每个单词首字母大写，部分特殊规定的除外，如 `{{archive.Id}}`、`{{archive.Title}}`

**编码要求：** 模板文件统一编码为UTF8编码，如果是其他编码，则会导致页面乱码，无法正常显示。如果是windows用户编辑模板文件的时候，请存储成UTF-8格式编码的模板文件。

#### 模板类型支持

AnqiCMS支持以下模板类型：

1. **自适应模板类型** - 一套模板适配所有设备
2. **代码适配模板类型** - 通过代码判断设备类型
3. **PC+手机端模板类型** - 分别制作PC和移动端模板

#### 移动端模板

模板支持单独的移动端模板定义，移动端模板存放在 `mobile` 目录。选择 **代码适配模式**、**PC+手机端模式** 的模板时，需要创建 `mobile` 模板。

#### 自定义模板名称

模板支持一些默认的自定义名称，只要存在这些自定义的模板文件，则不需要单独在后台设置模板，就能自动应用该模板。

已支持的模板名称情况：

- **文档默认自定义模板** - 名称格式是 `{模型table}/{文档id}.html`
- **文档列表默认自定义模板** - 名称格式是 `{模型table}/list-{分类id}.html`
- **单页面默认自定义模板** - 名称格式是 `page/{单页面id}.html`

### 模板目录结构和配置

#### 模板根目录

AnqiCMS 模板的根目录为 `/template`，每一套模板，都需要在 `/template` 下创建自己的模板目录，并在模板目录中添加 `config.json` 配置文件，来说明模板的一些信息。

#### config.json 配置文件

`config.json` 文件的内容格式为：

```json
{
    "name": "默认模板",
    "package": "default",
    "version": "1.0",
    "description": "系统默认模板",
    "author": "kandaoni.com",
    "homepage": "https://www.kandaoni.com",
    "created": "2022-05-10 22:29:00",
    "template_type": 0,
    "status": 0
}
```

**字段说明：**

- `name` - 模板名称，按照实际情况来取名
- `package` - 模板文件夹，仅支持英文字母和数字，请填写你的模板文件夹名称
- `version` - 模板的版本，自定义
- `description` - 模板的介绍
- `author` - 模板作者
- `homepage` - 模板作者的网站
- `created` - 模板创建时间，格式为：`2022-05-10 22:29:00`
- `template_type` - 模板类型，可选值为：0 自适应；1 代码适配；2 电脑+手机
- `status` - 模板使用状态，可选值为：0 未启用，1 使用中。所有的模板中，只能有一套模板的 `status` 值为 1

**注意：** `config.json` 的字段都是选填的，如果都不填，系统将会根据实际情况生成。

#### 模板目录组织模式

AnqiCMS支持2种模板目录组织模式：

##### 1. 文件夹组织模式

```
template/
├── config.json          # 模板配置文件
├── bash.html           # 公共代码（页头、页脚等）
├── partial/             # 代码片段目录
│   ├── sidebar.html     # 侧边栏
│   └── breadcrumb.html  # 面包屑
├── index/
│   └── index.html       # 首页
├── {模型table}/
│   ├── index.html       # 模型首页
│   ├── detail.html      # 文档详情页
│   ├── detail-{文档ID}.html  # 指定文档详情页
│   ├── list.html        # 文档列表页
│   └── list-{分类ID}.html    # 指定分类列表页
├── comment/
│   └── list.html        # 评论列表页
├── guestbook/
│   └── index.html       # 在线留言页
├── page/
│   ├── detail.html      # 单页面详情页
│   └── detail-{单页ID}.html  # 指定单页详情页
├── search/
│   └── index.html       # 搜索页
├── tag/
│   ├── index.html       # 标签首页
│   └── list.html        # 标签文档列表页
├── errors/
│   ├── 404.html         # 404错误页
│   ├── 500.html         # 500错误页
│   └── close.html       # 站点关闭提示页
└── mobile/              # 手机端模板目录
    └── ...              # 与上面结构一致
```

##### 2. 扁平化文件组织模式

```
template/
├── config.json          # 模板配置文件
├── bash.html           # 公共代码
├── partial/             # 代码片段目录
├── index.html           # 首页
├── {模型table}_index.html    # 模型首页
├── {模型table}_detail.html   # 文档详情页
├── {模型table}_list.html     # 文档列表页
├── comment_list.html    # 评论列表页
├── guestbook.html       # 在线留言页
├── page.html            # 单页面详情页
├── page-{单页ID}.html    # 指定单页详情页
├── search.html          # 搜索页
├── tag_index.html       # 标签首页
├── tag_list.html        # 标签文档列表页
├── errors_404.html      # 404错误页
├── errors_500.html      # 500错误页
├── errors_close.html    # 站点关闭提示页
└── mobile/              # 手机端模板目录
    └── ...              # 与上面结构一致
```

#### 自定义模板名称

部分模板还支持自定义名称，如文档详情、分类页、单页面，可以增加其他文件名，来处理不同的分类、页面需要不同的模板的情况。

**示例：** 如需要对 `关于我们` 这个单页，采用独立的模板，则可以命名模板为 `page/about.html`，并在后台创建一个调用自定义模板为 `page/about.html` 的关于我们页面即可。

### 模板标签概览

AnqiCMS 模板文件使用类似 Django 模板引擎的标签标记。系统内置了38种常用标签，涵盖了网站开发的各个方面：

#### 核心标签分类

1. **系统信息标签** - `system`、`contact`、`tdk`
2. **导航标签** - `navList`、`breadcrumb`
3. **分类标签** - `categoryList`、`categoryDetail`
4. **页面标签** - `pageList`、`pageDetail`
5. **文档标签** - `archiveList`、`archiveDetail`、`prevArchive`、`nextArchive`
6. **标签系统** - `tagList`、`tagDetail`、`tagDataList`
7. **交互功能** - `commentList`、`guestbook`、`linkList`
8. **辅助功能** - `pagination`、`archiveParams`、`archiveFilters`
9. **逻辑控制** - `if`、`for`、`with`
10. **工具函数** - `stampToDate`、各种过滤器

#### 标签使用示例

```html
<!-- 系统设置标签 -->
{% system with name="SiteName" %}

<!-- 文档列表标签 -->
{% archiveList archives with limit="10" %}
{% for item in archives %}
<div>{{item.Title}}</div>
{% endfor %}
{% endarchiveList %}

<!-- 条件判断标签 -->
{% if archive.Thumb %}
<img src="{{archive.Thumb}}" alt="{{archive.Title}}" />
{% endif %}

<!-- 时间格式化 -->
{{stampToDate(archive.CreatedTime, "2006-01-02")}}
```

---

## 文档标签相关

### 文档列表标签

**说明：** 用于获取文档列表数据

**使用方法：** `{% archiveList 变量名称 with 参数 %}`

**支持的参数：**

- **文档模型 ID** `moduleId` - 指定文档模型，如果不指定，则获取所有模型的文档
- **分类 ID** `categoryId` - 指定分类，如果不指定，则获取所有分类的文档
- **文档 Flag** `flag` - 指定文档标识，如推荐、置顶等
- **关键词** `q` - 搜索关键词
- **排序方式** `order` - 支持多种排序方式
- **数量限制** `limit` - 限制返回的文档数量
- **偏移量** `offset` - 跳过指定数量的文档
- **站点 ID** `siteId` - 多站点时指定站点

**代码示例：**

```html
<!-- 获取最新10篇文档 -->
{% archiveList archives with limit="10" %}
{% for item in archives %}
<div>
    <h3><a href="{{item.Link}}">{{item.Title}}</a></h3>
    <p>{{item.Description}}</p>
    <span>{{item.CreatedTime}}</span>
</div>
{% endfor %}
{% endarchiveList %}

<!-- 获取推荐文档 -->
{% archiveList hotArchives with flag="h" limit="5" %}
{% for item in hotArchives %}
<div>
    <a href="{{item.Link}}">
        <img src="{{item.Thumb}}" alt="{{item.Title}}" />
        <span>{{item.Title}}</span>
    </a>
</div>
{% endfor %}
{% endarchiveList %}
```

### 文档详情标签

**说明：** 用于获取文档详情数据

**使用方法：** `{% archiveDetail 变量名称 with name="字段名称" id="1" %}`

变量名称不是必须的，设置了变量名称后，后续可以通过变量名称来调用，而不设置变量名称，则是直接输出结果。

**archiveDetail 支持的参数有：**

- **文档 ID** `id` - id 不是必须的，默认会获取当前文档。如果需要指定文档，可以通过设置 id 来达到目的。
- **文档 URL 别名** `token` - token 不是必须的，默认会获取当前文档。如果需要指定文档，可以通过设置 id 或 token 来达到目的。
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据。

**name 参数可用的字段有：**

- **文档 ID** `Id`
- **文档标题** `Title`
- **文档 SEO 标题** `SeoTitle`
- **文档链接** `Link`
- **文档关键词** `Keywords`
- **文档描述** `Description`
- **文档内容** `Content` - 支持图片懒加载，需要使用标签 `lazy="data-src"`
- **文档内容的标题** `ContentTitles` - 返回的是一个数组
- **文档模型 ID** `ModuleId`
- **文档分类 ID** `CategoryId`
- **文档规范链接** `CanonicalUrl`
- **文档的用户 ID** `UserId`
- **价格** `Price`
- **库存** `Stock`
- **文档阅读等级** `ReadLevel`
- **文档的原文链接** `OriginUrl`
- **文档浏览量** `Views`
- **文档的 Flag 属性** `Flag`
- **文档封面图片** `Images`
- **文档封面首图** `Logo`
- **文档封面缩略图** `Thumb`
- **文档评论数量** `CommentCount`
- **文档分类** `Category`
- **文档添加时间** `CreatedTime` - 时间戳，需要使用格式化时间戳为日期格式
- **文档更新时间** `UpdatedTime` - 时间戳，需要使用格式化时间戳为日期格式
- **文档标签**
- **文档模型设置的其他字段参数**

**代码示例：**

```html
<!-- 文档标题 -->
<h1>{% archiveDetail with name="Title" %}</h1>

<!-- 文档内容 -->
<div>{% archiveDetail articleContent with name="Content" %}{{articleContent|safe}}</div>

<!-- 文档封面图 -->
<img src="{% archiveDetail with name="Logo" %}" alt="{% archiveDetail with name="Title" %}" />

<!-- 文档添加时间 -->
<span>{% archiveDetail with name="CreatedTime" format="2006-01-02" %}</span>

<!-- 文档分类 -->
{% archiveDetail archiveCategory with name="Category" %}
<a href="{{ archiveCategory.Link }}">
    <span>{{archiveCategory.Title}}</span>
</a>
```

### 上一篇文档标签

**说明：** 用于获取上一篇文档数据

**使用方法：** `{% prevArchive 变量名称 %}`

如将变量定义为 prev `{% prevArchive prev %}...{% endprevArchive %}`

prevArchive 不支持参数。

**prevArchive 支持的字段有：**

- 文档 ID `Id`
- 文档标题 `Title`
- 文档链接 `Link`
- 文档关键词 `Keywords`
- 文档描述 `Description`
- 文档分类 ID `CategoryId`
- 文档浏览量 `Views`
- 文档封面首图 `Logo`
- 文档封面缩略图 `Thumb`
- 文档评论数量 `CommentCount`
- 文档添加时间 `CreatedTime`
- 文档更新时间 `UpdatedTime`

**代码示例：**

```html
{% prevArchive prev %}
上一篇：
{% if prev %}
<a href="{{prev.Link}}">{{prev.Title}}</a>
{% else %}
没有了
{% endif %}
{% endprevArchive %}

<!-- 显示图片 -->
{% prevArchive prev %}
上一篇：
{% if prev %}
<a href="{{prev.Link}}">
    <img src="{{prev.Thumb}}" />
    <span>{{prev.Title}}</span>
</a>
{% else %}
没有了
{% endif %}
{% endprevArchive %}
```

### 下一篇文档标签

**说明：** 用于获取下一篇文档数据

**使用方法：** `{% nextArchive 变量名称 %}`

如将变量定义为 next `{% nextArchive next %}...{% endnextArchive %}`

nextArchive 不支持参数。

**nextArchive 支持的字段有：**

- 文档 ID `Id`
- 文档标题 `Title`
- 文档链接 `Link`
- 文档关键词 `Keywords`
- 文档描述 `Description`
- 文档分类 ID `CategoryId`
- 文档浏览量 `Views`
- 文档封面首图 `Logo`
- 文档封面缩略图 `Thumb`
- 文档评论数量 `CommentCount`
- 文档添加时间 `CreatedTime`
- 文档更新时间 `UpdatedTime`

**代码示例：**

```html
{% nextArchive next %}
下一篇：
{% if next %}
<a href="{{next.Link}}">{{next.Title}}</a>
{% else %}
没有了
{% endif %}
{% endnextArchive %}

<!-- 显示图片 -->
{% nextArchive next %}
下一篇：
{% if next %}
<a href="{{next.Link}}">
    <img src="{{next.Thumb}}" />
    <span>{{next.Title}}</span>
</a>
{% else %}
没有了
{% endif %}
{% endnextArchive %}
```

### 相关文档标签

**说明：** 获取当前文档的相关文档。相关文档的逻辑是：根据当前文档的文档 id，获取同分类的临近文档。因此该标签只能在文档详情页使用。

**使用方法：** `{% archiveList 变量名称 with type="related" limit="10" %}`

如将变量定义为 archives `{% archiveList archives with type="related" limit="10" %}...{% endarchiveList %}`

type 的值为 related 的情况下，不支持 order 参数。

type 的值为 related 的情况下，支持 like 参数。
- `like="keywords|relation"`，默认不需要 like 参数，它自动获取最靠近当前文档的其他文档
- 如果指定了 `like="keywords"`，则会根据文档第一个关键词来获取相关的文档
- 如果指定了 `like="relation"`，则只会展示后台文档编辑界面设置的相关文档

**代码示例：**

```html
<!-- related 相关文档列表展示 -->
<div>
{% archiveList archives with type="related" limit="10" %}
{% for item in archives %}
<li>
    <a href="{{item.Link}}">
        <h5>{{item.Title}}</h5>
        <div>{{item.Description}}</div>
        <div>
            <span>{% categoryDetail with name="Title" id=item.CategoryId %}</span>
            <span>{{stampToDate(item.CreatedTime, "2006-01-02")}}</span>
            <span>{{item.Views}} 阅读</span>
        </div>
    </a>
    {% if item.Thumb %}
    <a href="{{item.Link}}">
        <img alt="{{item.Title}}" src="{{item.Thumb}}">
    </a>
    {% endif %}
</li>
{% empty %}
<li>该列表没有任何内容</li>
{% endfor %}
{% endarchiveList %}
</div>
```

### 文档参数标签

**说明：** 用于获取指定文档的后台设置的参数

**使用方法：** `{% archiveParams 变量名称 with id="1" sorted=true %}`

如将变量定义为 params `{% archiveParams params with id="1" sorted=true %}...{% endarchiveParams %}`

**archiveParams 支持的参数有：**

- **文档 ID** `id` - id 参数根据文档 id 获取指定的文档参数，默认获取当前文档页面的文档 id
- **是否排序** `sorted` - 支持的值有：false|true
  - `sorted=false` 时，获取的是一个无序的 map 对象，需要用.的形式获取数据
  - `sorted=true` 时，获取是一个固定排序的数组对象。默认是 true
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**单个字段内的结构是：**
- 字段名称 `Name`
- 字段数据 `Value`

**代码示例：**

```html
<!-- 固定排序的数组 -->
<div>
{% archiveParams params %}
{% for item in params %}
<div>
    <span>{{item.Name}}：</span>
    <span>{{item.Value}}</span>
</div>
{% endfor %}
{% endarchiveParams %}
</div>

<!-- 指定文档ID -->
<div>
{% archiveParams params with id="1" %}
{% for item in params %}
<div>
    <span>{{item.Name}}：</span>
    <span>{{item.Value}}</span>
</div>
{% endfor %}
{% endarchiveParams %}
</div>

<!-- 无序的map对象 -->
<div>
{% archiveParams params with sorted=false %}
<div>{{params.yuedu.Name}}:{{params.yuedu.Value}}</div>
<div>{{params.danxuan.Name}}:{{params.danxuan.Value}}</div>
<div>{{params.duoxuan.Name}}:{{params.duoxuan.Value}}</div>
{% endarchiveParams %}
</div>
```

### 文档参数筛选标签

**说明：** 文档参数筛选仅可用着文档首页或文档分类的模板上，结合文档分页列表使用。用于做根据文档各项参数进行列表组合筛选的筛选条件，如做房产网站的时候，可以根据房屋类型为住宅、商铺、商住两用筛选，同时可以附加根据房屋大小为单间、一室一厅、两室两厅、三室两厅等筛选。

**使用方法：** `{% archiveFilters 变量名 with allText="全部" %}`

如将变量定义为 filters `{% archiveFilters filters with allText="全部" %}...{% endarchiveFilters %}`

**archiveFilters 支持的参数有：**

- **模型 ID** `moduleId` - moduleId 可以获取指定模型的参数筛选，如 moduleId="1" 获取文章模型的参数筛选
- **全部关键词** `allText` - allText 设置全部关键词是文字内容，如"全部"，如果不想显示，则设置 allText=false
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**filters 变量为一个数组对象，需要通过 for 循环来输出。for item 的对象结构是：**

- 参数名称 `Name`
- 参数字段名 `FieldName`
- 该参数的可选值 `Items`

**Items 是一个数组对象，需要通过 for 循环来输出。for val 的对象结构是：**

- 筛选值 `Label`
- 筛选值链接 `Link`
- 是否选中 `IsCurrent`

**代码示例：**

```html
<!-- 参数筛选代码 -->
<div>
    <div>参数筛选：</div>
    {% archiveFilters filters with moduleId="1" allText="默认" %}
    {% for item in filters %}
    <ul>
        <li>{{item.Name}}: </li>
        {% for val in item.Items %}
        <li class="{% if val.IsCurrent %}active{% endif %}">
            <a href="{{val.Link}}">{{val.Label}}</a>
        </li>
        {% endfor %}
    </ul>
    {% endfor %}
    {% endarchiveFilters %}
</div>

<!-- 房产筛选示例 -->
<div>
    <div>房产筛选：</div>
    {% archiveFilters filters with moduleId="1" allText="不限" %}
    {% for item in filters %}
    <ul>
        <li>{{item.Name}}: </li>
        {% for val in item.Items %}
        <li class="{% if val.IsCurrent %}active{% endif %}">
            <a href="{{val.Link}}">{{val.Label}}</a>
        </li>
        {% endfor %}
    </ul>
    {% endfor %}
    {% endarchiveFilters %}
</div>
```

## 分类页面标签相关

### 分类列表标签

**说明：** 用于获取文章、产品分类列表

**使用方法：** `{% categoryList 变量名称 with moduleId="1|2|3" parentId="0" %}`

如将变量定义为 categories `{% categoryList categories with moduleId="1" parentId="0" %}...{% endcategoryList %}`

**categoryList 支持的参数有：**

- **模型 ID** `moduleId` - moduleId 可以获取指定文档模型的分类列表如 moduleId="1" 获取文章模型的分类列表
- **上级分类** `parentId` - parentId 表示上级分类，可以获取指定上级分类下的子分类，parentId="parent" 表示上级分类为当前分类的上级分类，表示获取当前分类的兄弟分类。parentId="0"的时候，获取顶级分类。当要获取顶级分类的时候，必须指定模型 ID moduleId
- **获取全部分类** `all` - all 可以获取指定所有分类列表，如 all=true 获取所有分类，如果同时指定了moduleId，则获取指定模型下的所有分类
- **显示数量** `limit` - limit 可以指定显示数量，比如limit="10"则只会显示 10 条，limit 支持offset模式，也就是 ,分隔模式，如想从第 2 条开始，获取 10 条数据，可以设置成 limit="2,10"
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**categories 是一个数组对象，因此需要使用 for 循环来输出，item 为 for 循环体内的变量，可用的字段有：**

- 分类 ID `Id`
- 分类标题 `Title`
- 分类链接 `Link`
- 分类描述 `Description`
- 分类内容 `Content`
- 上级分类 ID `ParentId`
- 分类缩略图大图 `Logo`
- 分类缩略图 `Thumb`
- 下级分类前缀 `Spacer`
- 是否有下级分类 `HasChildren`
- 是否当前链接 `IsCurrent`
- 分类的文档数量 `ArchiveCount`

**代码示例：**

```html
{% categoryList categories with moduleId="1" parentId="0" %}
<ul>
{% for item in categories %}
<li>
    <!-- 如需判断当前是否是循环中的第一条，可以这么写： -->
    {% if forloop.Counter == 1 %}这是第一条{% endif %}
    <!-- 比如需要给第一条添加额外class="active"，可以这么写： -->
    <a class="{% if forloop.Counter == 1 %}active{% endif %}" href="{{item.Link}}">{{item.Title}}</a>
    <a href="{{ item.Link }}">{{item.Spacer|safe}}{{item.Title}}</a>
    <a href="{{ item.Link }}">
        <span>当前第{{ forloop.Counter }}个，剩余{{ forloop.Revcounter}}个</span>
        <span>分类ID：{{item.Id}}</span>
        <span>分类名称：{{item.Title}}</span>
        <span>分类链接：{{item.Link}}</span>
        <span>分类描述：{{item.Description}}</span>
        <span>分类内容：{{item.Content|safe}}</span>
        <span>上级分类ID：{{item.ParentId}}</span>
        <span>下级分类前缀：{{item.Spacer|safe}}</span>
        <span>是否有下级分类：{{item.HasChildren}}</span>
    </a>
    <div>缩略图大图：<img style="width: 200px" src="{{item.Logo}}" alt="{{item.Title}}" /></div>
    <div>缩略图：<img style="width: 200px" src="{{item.Thumb}}" alt="{{item.Title}}" /></div>
</li>
{% endfor %}
</ul>
{% endcategoryList %}
```

### 分类详情标签

**说明：** 用于获取文章分类、产品分类详情

**使用方法：** `{% categoryDetail 变量名称 with name="字段名称" id="1" %}`

变量名称不是必须的，设置了变量名称后，后续可以通过变量名称来调用，而不设置变量名称，则是直接输出结果。

**categoryDetail 支持的参数有：**

- **分类 ID** `id` - id 不是必须的，默认会获取当前分类。如果需要指定分类，可以通过设置 id 来达到目的
- **分类 URL 别名** `token` - token 不是必须的，默认会获取当前分类。如果需要指定分类，可以通过设置 id 或 token 来达到目的
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**name 参数可用的字段有：**

- 分类 ID `Id`
- 分类标题 `Title`
- 分类 SEO 标题 `SeoTitle`
- 分类链接 `Link`
- 分类关键词 `Keywords`
- 分类描述 `Description`
- 分类内容 `Content`
- 分类模型 ID `ModuleId`
- 上级分类 ID `ParentId`
- 分类缩略图大图 `Logo`
- 分类缩略图 `Thumb`
- 分类的文档数量 `ArchiveCount`

**代码示例：**

```html
<!-- 分类标题 -->
<h1>{% categoryDetail with name="Title" %}</h1>

<!-- 分类描述 -->
<div>{% categoryDetail with name="Description" %}</div>

<!-- 分类内容 -->
<div>{% categoryDetail categoryContent with name="Content" %}{{categoryContent|safe}}</div>

<!-- 分类缩略图 -->
<img src="{% categoryDetail with name="Logo" %}" alt="{% categoryDetail with name="Title" %}" />

<!-- 获取指定分类信息 -->
<div>分类标题：{% categoryDetail with name="Title" id="1" %}</div>
```

### 单页列表标签

**说明：** 用于获取单页列表

**使用方法：** `{% pageList 变量名称 %}`

如将变量定义为 pages `{% pageList pages %}...{% endpageList %}`

**pageList 支持的参数：**

- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**pages 是一个数组对象，因此需要使用 for 循环来输出，item 为 for 循环体内的变量，可用的字段有：**

- 单页 ID `Id`
- 单页标题 `Title`
- 单页链接 `Link`
- 单页描述 `Description`
- 单页内容 `Content`
- 单页缩略图大图 `Logo`
- 单页缩略图 `Thumb`

**代码示例：**

```html
<ul>
{% pageList pages %}
{% for item in pages %}
<li>
    <!-- 如需判断当前是否是循环中的第一条，可以这么写： -->
    {% if forloop.Counter == 1 %}这是第一条{% endif %}
    <!-- 比如需要给第一条添加额外class="active"，可以这么写： -->
    <a class="{% if forloop.Counter == 1 %}active{% endif %}" href="{{item.Link}}">{{item.Title}}</a>
    <a href="{{ item.Link }}">{{item.Title}}</a>
    <a href="{{ item.Link }}">
        <span>当前第{{ forloop.Counter }}篇，剩余{{ forloop.Revcounter}}篇</span>
        <span>单页ID：{{item.Id}}</span>
        <span>单页名称：{{item.Title}}</span>
        <span>单页链接：{{item.Link}}</span>
        <span>单页描述：{{item.Description}}</span>
        <span>单页内容：{{item.Content|safe}}</span>
    </a>
    <div>缩略图大图：<img src="{{item.Logo}}" alt="{{item.Title}}" /></div>
    <div>缩略图：<img src="{{item.Thumb}}" alt="{{item.Title}}" /></div>
</li>
{% endfor %}
{% endpageList %}
</ul>

<!-- 排除id为1的页面 -->
{% pageList pages %}
{% for item in pages %}
{% if item.Id != 1 %}
<li>
    <a href="{{ item.Link }}">{{item.Title}}</a>
    <div>{{item.Description}}</div>
</li>
{% endif %}
{% endfor %}
{% endpageList %}
```

### 单页详情标签

**说明：** 用于获取单页详情数据

**使用方法：** `{% pageDetail 变量名称 with name="字段名称" id="1" %}`

变量名称不是必须的，设置了变量名称后，后续可以通过变量名称来调用，而不设置变量名称，则是直接输出结果。

**pageDetail 支持的参数有：**

- **单页 ID** `id` - id 不是必须的，默认会获取当前单页。如果需要指定单页，可以通过设置 id 来达到目的
- **单页 URL 别名** `token` - token 不是必须的，默认会获取当前单页。如果需要指定单页，可以通过设置 id 或 token 来达到目的
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**代码示例：**

```html
<!-- 单页标题 -->
<h1>{% pageDetail with name="Title" %}</h1>

<!-- 单页内容 -->
<div>{% pageDetail pageContent with name="Content" %}{{pageContent|safe}}</div>

<!-- 单页描述 -->
<div>{% pageDetail with name="Description" %}</div>

<!-- 获取指定单页信息 -->
<div>单页标题：{% pageDetail with name="Title" id="1" %}</div>
```

## 文档Tag标签相关

### 文档Tag列表标签

**说明：** 当我们跟文档打上各种tag后，我们就可以用文档Tag列表标签来调用这些Tag。

**使用方法：** `{% tagList 变量名 with limit="10" %}`

如将变量定义为 tags `{% tagList tags with limit="10" %}...{% endtagList %}`

**tagList 支持的参数有：**

- **数量限制** `limit` - 限制返回的Tag数量
- **站点 ID** `siteId` - 多站点时指定站点

**代码示例：**

```html
{% tagList tags with limit="10" %}
{% for item in tags %}
<a href="{{item.Link}}">{{item.Title}}</a>
{% endfor %}
{% endtagList %}
```

### Tag文档列表标签

**说明：** 用于获取指定Tag的文档列表

**使用方法：** `{% tagDataList 变量名称 with tagId="1" %}`

如将变量定义为 archives `{% tagDataList archives with tagId="1" %}...{% endtagDataList %}`

**tagDataList 支持的参数有：**

- **Tag ID** `tagId` - 指定Tag的ID获取对应的文档列表
- **数量限制** `limit` - 限制返回的文档数量
- **排序方式** `order` - 支持多种排序方式
- **站点 ID** `siteId` - 多站点时指定站点

**archives 是一个数组对象，item 为 for 循环体内的变量，可用的字段有：**

- 文档 ID `Id`
- 文档标题 `Title`
- 文档链接 `Link`
- 文档关键词 `Keywords`
- 文档描述 `Description`
- 文档内容 `Content`
- 文档分类 ID `CategoryId`
- 文档浏览量 `Views`
- 文档封面首图 `Logo`
- 文档封面缩略图 `Thumb`
- 文档添加时间 `CreatedTime`
- 文档更新时间 `UpdatedTime`

**代码示例：**

```html
<!-- 获取指定Tag的文档列表 -->
{% tagDataList archives with tagId="1" limit="10" %}
{% for item in archives %}
<div class="tag-article">
    <h3><a href="{{item.Link}}">{{item.Title}}</a></h3>
    <p>{{item.Description}}</p>
    <div class="meta">
        <span>{{stampToDate(item.CreatedTime, "2006-01-02")}}</span>
        <span>阅读 {{item.Views}} 次</span>
    </div>
</div>
{% endfor %}
{% endtagDataList %}

<!-- 带缩略图的Tag文档列表 -->
{% tagDataList archives with tagId="2" limit="6" %}
<div class="tag-articles-grid">
{% for item in archives %}
<div class="article-card">
    {% if item.Thumb %}
    <img src="{{item.Thumb}}" alt="{{item.Title}}" />
    {% endif %}
    <h4><a href="{{item.Link}}">{{item.Title}}</a></h4>
    <p>{{item.Description|truncatechars:100}}</p>
</div>
{% endfor %}
</div>
{% endtagDataList %}
```

### Tag详情标签

**说明：** 用于获取文档的Tag详情数据

**使用方法：** `{% tagDetail 变量名称 with name="字段名称" id="1" %}`

变量名称不是必须的，设置了变量名称后，后续可以通过变量名称来调用，而不设置变量名称，则是直接输出结果。

**tagDetail 支持的参数有：**

- **Tag ID** `id` - id 不是必须的，默认会获取当前Tag。如果需要指定Tag，可以通过设置 id 来达到目的
- **Tag URL 别名** `token` - token 不是必须的，默认会获取当前Tag。如果需要指定Tag，可以通过设置 id 或 token 来达到目的
- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**name 参数可用的字段有：**

- Tag ID `Id`
- Tag标题 `Title`
- Tag链接 `Link`
- Tag描述 `Description`
- Tag的文档数量 `ArchiveCount`

**代码示例：**

```html
<!-- Tag标题 -->
<h1>{% tagDetail with name="Title" %}</h1>

<!-- Tag描述 -->
<div>{% tagDetail with name="Description" %}</div>

<!-- Tag文档数量 -->
<span>共有 {% tagDetail with name="ArchiveCount" %} 篇文档</span>

<!-- 获取指定Tag信息 -->
{% tagDetail tag with id="1" %}
<div class="tag-info">
    <h2>{{tag.Title}}</h2>
    <p>{{tag.Description}}</p>
    <span>包含 {{tag.ArchiveCount}} 篇文档</span>
</div>

<!-- Tag面包屑导航 -->
<nav class="tag-breadcrumb">
    <a href="/">首页</a> >
    <a href="/tags/">标签</a> >
    <span>{% tagDetail with name="Title" %}</span>
</nav>
```

## 常用标签相关

### 系统设置标签

**说明：** 用于获取系统配置信息

**使用方法：** `{% system 变量名称 with name="字段名称" %}`

变量名称不是必须的，设置了变量名称后，后续可以通过变量名称来调用，而不设置变量名称，则是直接输出结果。

**name 参数可用的字段名称有：**

- 网站名称 `SiteName`
- 网站Logo `SiteLogo`
- 网站域名 `BaseUrl`
- 网站关键词 `SiteKeywords`
- 网站描述 `SiteDescription`
- 网站ICP备案号 `SiteIcp`
- 网站版权信息 `SiteCopyright`

**代码示例：**

```html
<!-- 网站名称 -->
<title>{% system with name="SiteName" %}</title>

<!-- 网站Logo -->
<img src="{% system with name="SiteLogo" %}" alt="{% system with name="SiteName" %}" />

<!-- 网站关键词 -->
<meta name="keywords" content="{% system with name="SiteKeywords" %}" />

<!-- 网站描述 -->
<meta name="description" content="{% system with name="SiteDescription" %}" />
```

### 联系方式标签

**说明：** 用于获取后台配置的联系方式信息

**使用方法：** `{% contact 变量名称 with name="字段名称" %}`

**name 参数可用的字段名称有：**

- 联系人 `UserName`
- 联系电话 `Cellphone`
- 座机电话 `Phone`
- 邮箱地址 `Email`
- 微信号 `Wechat`
- QQ号码 `QQ`
- 地址 `Address`

**代码示例：**

```html
<!-- 联系电话 -->
<a href="tel:{% contact with name='Cellphone' %}">{% contact with name="Cellphone" %}</a>

<!-- 邮箱地址 -->
<a href="mailto:{% contact with name='Email' %}">{% contact with name="Email" %}</a>

<!-- 联系地址 -->
<div>地址：{% contact with name="Address" %}</div>
```

### TDK标签

**说明：** 用于获取页面的title、keywords、description信息

**使用方法：** `{% tdk 变量名称 with name="字段名称" %}`

**name 参数可用的字段名称有：**

- 页面标题 `Title`
- 页面关键词 `Keywords`
- 页面描述 `Description`

**代码示例：**

```html
<title>{% tdk with name="Title" %}</title>
<meta name="keywords" content="{% tdk with name="Keywords" %}" />
<meta name="description" content="{% tdk with name="Description" %}" />
```

### 导航列表标签

**说明：** 用于获取页面导航列表

**使用方法：** `{% navList 变量名称 %}`

如将变量定义为navs `{% navList navs %}...{% endnavList %}`

**代码示例：**

```html
{% navList navs %}
<ul>
{% for item in navs %}
<li>
    <a href="{{item.Link}}" {% if item.IsCurrent %}class="active"{% endif %}>
        {{item.Title}}
    </a>
    {% if item.Children %}
    <ul>
    {% for child in item.Children %}
    <li><a href="{{child.Link}}">{{child.Title}}</a></li>
    {% endfor %}
    </ul>
    {% endif %}
</li>
{% endfor %}
</ul>
{% endnavList %}
```

### 面包屑导航标签

**说明：** 用于获取面包屑导航列表

**使用方法：** `{% breadcrumb 变量名称 with index="首页" %}`

如将变量定义为 crumbs `{% breadcrumb crumbs with index="首页" %}...{% endbreadcrumb %}`

**代码示例：**

```html
{% breadcrumb crumbs with index="首页" %}
<nav>
{% for item in crumbs %}
{% if not forloop.Last %}
<a href="{{item.Link}}">{{item.Title}}</a> >
{% else %}
<span>{{item.Title}}</span>
{% endif %}
{% endfor %}
</nav>
{% endbreadcrumb %}
```

### 统计代码标签

**说明：** 用于获取后台设置的统计代码

**使用方法：** `{% statistic 变量名称 with name="字段名称" %}`

**name 参数可用的字段名称有：**

- 统计代码 `StatisticCode`
- 百度统计 `BaiduStatistic`
- 谷歌统计 `GoogleStatistic`

**代码示例：**

```html
<!-- 输出统计代码 -->
{% statistic with name="StatisticCode" %}

<!-- 输出百度统计代码 -->
{% statistic with name="BaiduStatistic" %}

<!-- 输出谷歌统计代码 -->
{% statistic with name="GoogleStatistic" %}
```

### 首页Banner列表标签

**说明：** 用于获取首页 Banner 列表

**使用方法：** `{% bannerList 变量名称 %}`

如将变量定义为 banners `{% bannerList banners %}...{% endbannerList %}`

**bannerList 支持的参数有：**

- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据
- **分组名称** `type` - type 默认值"default"，你可以在后台创建多个 banner 分组，然后通过`type="分组名"`，来调用不同分组的 banner

**banners 是一个数组对象，因此需要使用 for 循环来输出，item 为 for 循环体内的变量，可用的字段有：**

- ID `Id`
- 标题 `Title`
- Logo 图片地址 `Logo`
- 链接地址 `Link`
- 介绍 `Description`
- 图片 Alt `Alt`

**代码示例：**

```html
<!-- 基本Banner列表 -->
{% bannerList banners %}
{% for item in banners %}
<a href="{{item.Link}}" target="_blank">
    <img src="{{item.Logo}}" alt="{{item.Alt}}" />
    <h5>{{item.Title}}</h5>
</a>
{% endfor %}
{% endbannerList %}

<!-- 调用指定分组，如分组名是"幻灯" -->
{% bannerList banners with type="幻灯" %}
{% for item in banners %}
<a href="{{item.Link}}" target="_blank">
    <img src="{{item.Logo}}" alt="{{item.Alt}}" />
    <h5>{{item.Title}}</h5>
</a>
{% endfor %}
{% endbannerList %}

<!-- 给第一条添加额外class="active" -->
{% bannerList banners %}
{% for item in banners %}
<a class="{% if forloop.Counter == 1 %}active{% endif %}" href="{{item.Link}}" target="_blank">
    <img src="{{item.Logo}}" alt="{{item.Alt}}" />
    <h5>{{item.Title}}</h5>
</a>
{% endfor %}
{% endbannerList %}
```

### 文档模型详情标签

**说明：** 用于获取文档模型的详情信息

**使用方法：** `{% moduleDetail 变量名称 with name="字段名称" id="1" %}`

**name 参数可用的字段有：**

- 模型ID `Id`
- 模型名称 `Title`
- 模型表名 `TableName`
- 模型描述 `Description`
- 模型字段 `Fields`

**代码示例：**

```html
<!-- 获取当前模型名称 -->
<h1>{% moduleDetail with name="Title" %}</h1>

<!-- 获取指定模型信息 -->
{% moduleDetail module with id="1" %}
<div class="module-info">
    <h2>{{module.Title}}</h2>
    <p>{{module.Description}}</p>
</div>
```

## 其他标签相关

### 评论标列表签

**说明：** 用于获取文档的评论列表、评论分页列表

**使用方法：** `{% commentList 变量名称 with archiveId="1" type="page|list" %}`

如将变量定义为 comments `{% commentList comments with archiveId="1" type="list" %}...{% endcommentList %}`

**commentList 支持的参数有：**

- **文档 ID** `archiveId` - 指定文档ID获取评论
- **类型** `type` - page为分页模式，list为列表模式
- **数量限制** `limit` - 限制返回的评论数量
- **站点 ID** `siteId` - 多站点时指定站点

**代码示例：**

```html
{% commentList comments with archiveId="1" type="list" limit="10" %}
{% for item in comments %}
<div class="comment-item">
    <div class="comment-author">{{item.UserName}}</div>
    <div class="comment-content">{{item.Content}}</div>
    <div class="comment-time">{{stampToDate(item.CreatedTime, "2006-01-02 15:04")}}</div>
</div>
{% endfor %}
{% endcommentList %}
```

### 留言表单标签

**说明：** 用于获取后台设置的留言表单

**使用方法：** `{% guestbook 变量名称 %}`

如将变量定义为 fields `{% guestbook fields %}...{% endguestbook %}`

**guestbook 支持的参数有：**

- **站点 ID** `siteId` - siteId 一般不需要填写，如果你使用后台的多站点管理创建了多个站点，并且想调用其他站点的数据，则可以通过指定 siteId 来实现调用指定站点的数据

**fields 是一个数组对象，因此需要使用 for 循环来输出，item 为 for 循环体内的变量，可用的字段有：**

- 表单名称 `Name`
- 表单变量 `FieldName`
- 表单类型 `Type` - 文本类型 text、数字类型 number、多行文本类型 textarea、单项选择类型 radio、多项选择类型 checkbox、下拉选择类型 select
- 是否必填 `Required` - Required 值为 true 时，表示必填，Required 值为 false 时，表示可以不填
- 表单默认值 `Content`
- 分割成数组的默认值 `Items` - 当表单类型为单项选择类型 radio、多项选择类型 checkbox、下拉选择类型 select 时使用

**留言表单提交：**

留言表单提交需要使用 form 表单提交，提交后台接收地址为：`/guestbook.html`

**需要提交的字段有：**

- `user_name` - 是 - 留言的用户名
- `contact` - 是 - 联系方式，如手机，电话，微信，QQ 等
- `content` - 是 - 留言内容
- 其他自定义字段 - 根据设置决定 - 后台添加表单额外字段设置的字段
- `return` - 否 - 提交后，指定后端返回的格式，可选的值有：html、json，默认为 html

**代码示例：**

```html
<form method="post" action="/guestbook.html">
{% guestbook fields %}
{% for item in fields %}
<div>
    <label>{{item.Name}}</label>
    <div>
    {% if item.Type == "text" || item.Type == "number" %}
        <input type="{{item.Type}}" name="{{item.FieldName}}"
               {% if item.Required %}required{% endif %}
               placeholder="{{item.Content}}" autocomplete="off">
    {% elif item.Type == "textarea" %}
        <textarea name="{{item.FieldName}}"
                  {% if item.Required %}required{% endif %}
                  placeholder="{{item.Content}}" rows="5"></textarea>
    {% elif item.Type == "radio" %}
        {% for val in item.Items %}
        <input type="{{item.Type}}" name="{{item.FieldName}}"
               value="{{val}}" title="{{val}}">
        {% endfor %}
    {% elif item.Type == "checkbox" %}
        {% for val in item.Items %}
        <input type="{{item.Type}}" name="{{item.FieldName}}[]"
               value="{{val}}" title="{{val}}">
        {% endfor %}
    {% elif item.Type == "select" %}
        <select name="{{item.FieldName}}">
        {% for val in item.Items %}
        <option value="{{val}}">{{val}}</option>
        {% endfor %}
        </select>
    {% endif %}
    </div>
</div>
{% endfor %}
<div>
    <button type="submit">提交留言</button>
    <button type="reset">重置</button>
</div>
{% endguestbook %}
</form>
```

### 分页标签

**说明：** 用于获取文章列表、产品列表的分页信息

**使用方法：** `{% pagination 变量名称 with show="5" %}`

如将变量定义为 pages `{% pagination pages with show="5" %}...{% endpagination %}`

**pagination 支持的参数有：**

- **显示页数** `show` - 设置显示的页码数量，如 show="5" 表示显示5个页码

**代码示例：**

```html
{% pagination pages with show="5" %}
<nav class="pagination">
    <!-- 首页 -->
    <a class="{% if pages.FirstPage.IsCurrent %}active{% endif %}"
       href="{{pages.FirstPage.Link}}">{{pages.FirstPage.Name}}</a>

    <!-- 上一页 -->
    {% if pages.PrevPage %}
    <a href="{{pages.PrevPage.Link}}">{{pages.PrevPage.Name}}</a>
    {% endif %}

    <!-- 中间多页 -->
    {% for item in pages.Pages %}
    <a class="{% if item.IsCurrent %}active{% endif %}"
       href="{{item.Link}}">{{item.Name}}</a>
    {% endfor %}

    <!-- 下一页 -->
    {% if pages.NextPage %}
    <a href="{{pages.NextPage.Link}}">{{pages.NextPage.Name}}</a>
    {% endif %}

    <!-- 尾页 -->
    <a class="{% if pages.LastPage.IsCurrent %}active{% endif %}"
       href="{{pages.LastPage.Link}}">{{pages.LastPage.Name}}</a>
</nav>
{% endpagination %}
```

### 友情链接标签

**说明：** 用于获取友情链接列表

**使用方法：** `{% linkList 变量名称 %}`

如将变量定义为 friendLinks `{% linkList friendLinks %}...{% endlinkList %}`

**linkList 不支持参数，将会获取所有的友情链接。**

**friendLinks 是一个数组对象，因此需要使用 for 循环来输出，item 为 for 循环体内的变量，可用的字段有：**

- 链接 ID `Id`
- 链接标题 `Title`
- 链接地址 `Link`
- 链接描述 `Description`
- 链接Logo `Logo`

**代码示例：**

```html
{% linkList friendLinks %}
<div class="friend-links">
    <h3>友情链接</h3>
    <ul>
    {% for item in friendLinks %}
    <li>
        <a href="{{item.Link}}" target="_blank" title="{{item.Description}}">
            {% if item.Logo %}
            <img src="{{item.Logo}}" alt="{{item.Title}}" />
            {% endif %}
            {{item.Title}}
        </a>
    </li>
    {% endfor %}
    </ul>
</div>
{% endlinkList %}
```

### 留言验证码使用标签

**说明：** 用于在留言表单中显示验证码

**使用方法：** `{% captcha %}`

**代码示例：**

```html
<form method="post" action="/guestbook.html">
    <!-- 其他表单字段 -->
    <div>
        <label>验证码</label>
        <div>
            <input type="text" name="captcha" required placeholder="请输入验证码" />
            {% captcha %}
        </div>
    </div>
    <button type="submit">提交留言</button>
</form>
```

### 用户详情标签

**说明：** 用于获取用户详情信息

**使用方法：** `{% userDetail 变量名称 with name="字段名称" id="1" %}`

**支持的字段有：**

- 用户ID `Id`
- 用户名 `UserName`
- 真实姓名 `RealName`
- 邮箱 `Email`
- 手机号 `Phone`
- 头像 `Avatar`
- 用户组ID `GroupId`

**代码示例：**

```html
<!-- 获取当前用户信息 -->
<div class="user-info">
    <img src="{% userDetail with name='Avatar' %}" alt="用户头像" />
    <span>{% userDetail with name='UserName' %}</span>
</div>

<!-- 获取指定用户信息 -->
{% userDetail user with id="1" %}
<div class="author-info">
    <img src="{{user.Avatar}}" alt="{{user.UserName}}" />
    <span>{{user.RealName}}</span>
</div>
```

### 用户分组详情标签

**说明：** 用于获取用户分组详情信息

**使用方法：** `{% userGroupDetail 变量名称 with name="字段名称" id="1" %}`

**支持的字段有：**

- 分组ID `Id`
- 分组名称 `Title`
- 分组描述 `Description`

### 自定义内容标签

**说明：** 用于获取后台设置的自定义内容

**使用方法：** `{% customContent 变量名称 with name="内容名称" %}`

**代码示例：**

```html
<!-- 获取自定义内容 -->
{% customContent content with name="公司介绍" %}
<div class="company-intro">
    {{content|safe}}
</div>

<!-- 获取联系信息 -->
{% customContent contact with name="联系方式" %}
<div class="contact-info">
    {{contact|safe}}
</div>
```

### Json-LD自定义调用标签

**说明：** Json-LD 是一种用于描述网页内容的数据格式，它使用 JSON 来表示数据，并使用特定的语法来描述网页内容。后台功能开启了结构化数据功能后，默认会在页面中插入一个名为 `json-ld` 的标签，该标签的内容为结构化数据。

**使用方法：** `{% jsonLd %} ... {% endjsonLd %}`

如果你想更自由的控制 Json-LD 的内容，可以使用标签 `{% jsonLd %} ... {% endjsonLd %}`。

你只需要在包裹的内容中`{...}`里写上你需要自定义的字段即可，安企 CMS 会自动合并处理它们的数据，如果你定义的字段和默认的字段有冲突，则默认的字段会被覆盖。请注意，你定义的字段，一定要符合 JSON-LD 的语法。

**代码示例：**

```html
<!-- 自定义 author 字段，补充 images 字段 -->
{% jsonLd %}
<script type="application/ld+json">
{
	"author": "AnQiCMS",
	"image": [
		"https://www.anqicms.com/anqicms.png"
	]
}
</script>
{% endjsonLd %}

<!-- 自定义文章结构化数据 -->
{% jsonLd %}
<script type="application/ld+json">
{
	"@context": "https://schema.org",
	"@type": "Article",
	"author": {
		"@type": "Person",
		"name": "{% archiveDetail with name='Author' %}"
	},
	"publisher": {
		"@type": "Organization",
		"name": "{% system with name='SiteName' %}",
		"logo": {
			"@type": "ImageObject",
			"url": "{% system with name='SiteLogo' %}"
		}
	},
	"datePublished": "{% archiveDetail with name='CreatedTime' format='2006-01-02T15:04:05Z' %}",
	"dateModified": "{% archiveDetail with name='UpdatedTime' format='2006-01-02T15:04:05Z' %}"
}
</script>
{% endjsonLd %}
```

## 过滤器filter相关

AnQiCMS模板引擎支持丰富的过滤器，用于对变量进行格式化和处理。

### 常用过滤器

#### 文本处理过滤器

**1. safe - 安全输出HTML**
```html
{{content|safe}}  <!-- 输出HTML内容而不转义 -->
```

**2. length - 获取长度**
```html
{{title|length}}  <!-- 获取字符串长度 -->
{{list|length}}   <!-- 获取数组长度 -->
```

**3. truncatechars - 按字符截取**
```html
{{description|truncatechars:100}}  <!-- 截取前100个字符 -->
```

**4. truncatewords - 按单词截取**
```html
{{content|truncatewords:50}}  <!-- 截取前50个单词 -->
```

**5. upper/lower - 大小写转换**
```html
{{title|upper}}  <!-- 转换为大写 -->
{{title|lower}}  <!-- 转换为小写 -->
```

**6. title - 标题格式**
```html
{{title|title}}  <!-- 每个单词首字母大写 -->
```

**7. strip - 去除空格**
```html
{{text|strip}}  <!-- 去除前后空格 -->
```

#### 时间格式化过滤器

**1. stampToDate - 时间戳转日期**
```html
{{item.CreatedTime|stampToDate:"2006-01-02"}}  <!-- 格式化为 2021-06-30 -->
{{item.CreatedTime|stampToDate:"2006年01月02日"}}  <!-- 格式化为 2021年06月30日 -->
{{item.CreatedTime|stampToDate:"2006-01-02 15:04:05"}}  <!-- 格式化为 2021-06-30 12:30:45 -->
```

#### 数组和列表过滤器

**1. first/last - 获取首尾元素**
```html
{{list|first}}  <!-- 获取第一个元素 -->
{{list|last}}   <!-- 获取最后一个元素 -->
```

**2. join - 连接数组**
```html
{{tags|join:", "}}  <!-- 用逗号连接数组元素 -->
```

**3. slice - 切片**
```html
{{list|slice:":5"}}   <!-- 获取前5个元素 -->
{{list|slice:"2:7"}}  <!-- 获取第2到第7个元素 -->
```

#### HTML处理过滤器

**1. striptags - 移除HTML标签**
```html
{{content|striptags}}  <!-- 移除所有HTML标签 -->
```

**2. linebreaks - 换行转HTML**
```html
{{text|linebreaks}}  <!-- 将换行符转换为<p>和<br>标签 -->
```

**3. linebreaksbr - 换行转<br>**
```html
{{text|linebreaksbr}}  <!-- 将换行符转换为<br>标签 -->
```

**4. urlize - URL转链接**
```html
{{text|urlize}}  <!-- 将文本中的URL转换为可点击的链接 -->
```

#### 数字处理过滤器

**1. add - 加法运算**
```html
{{value|add:10}}  <!-- 给数值加10 -->
```

**2. floatformat - 浮点数格式化**
```html
{{price|floatformat:2}}  <!-- 保留2位小数 -->
```

#### 条件过滤器

**1. default - 默认值**
```html
{{value|default:"暂无"}}  <!-- 如果value为空，显示"暂无" -->
```

**2. default_if_none - 空值默认**
```html
{{value|default_if_none:"未设置"}}  <!-- 如果value为None，显示"未设置" -->
```

#### 特殊过滤器

**1. contains - 包含判断**
```html
{% if title|contains:"关键词" %}
包含关键词
{% endif %}
```

**2. random - 随机选择**
```html
{{list|random}}  <!-- 从列表中随机选择一个元素 -->
```

### 过滤器链式使用

可以将多个过滤器链式组合使用：

```html
{{content|striptags|truncatechars:100|default:"暂无内容"}}
<!-- 先移除HTML标签，再截取100字符，如果为空则显示"暂无内容" -->

{{title|lower|title}}
<!-- 先转小写，再转标题格式 -->

{{item.CreatedTime|stampToDate:"2006-01-02"|default:"未知时间"}}
<!-- 格式化时间，如果为空显示"未知时间" -->
```

### 自定义过滤器使用示例

```html
<!-- 文档列表中的应用 -->
{% archiveList archives with limit="10" %}
{% for item in archives %}
<div class="article-item">
    <h3><a href="{{item.Link}}">{{item.Title|truncatechars:50}}</a></h3>
    <p>{{item.Description|striptags|truncatechars:100|default:"暂无描述"}}</p>
    <div class="meta">
        <span>{{item.CreatedTime|stampToDate:"2006-01-02"}}</span>
        <span>阅读 {{item.Views|default:0}} 次</span>
    </div>
</div>
{% endfor %}
{% endarchiveList %}

<!-- 标签列表的应用 -->
{% tagList tags with limit="20" %}
<div class="tags">
{% for tag in tags %}
<a href="{{tag.Link}}" class="tag">{{tag.Title|title}}</a>
{% endfor %}
</div>
{% endtagList %}
```

### 完整过滤器列表

AnQiCMS提供了40个专业过滤器，以下是完整的过滤器列表和使用说明：

#### 字符串判断和处理过滤器

**1. contain - 判断是否包含指定关键词**
```html
{{text|contain:"关键词"}}  <!-- 返回布尔值 -->
{% if title|contain:"CMS" %}包含CMS{% endif %}
```

**2. trim - 删除前导和尾随空格**
```html
{{text|trim}}  <!-- 去除前后空格 -->
{{text|trim:" "}}  <!-- 去除指定字符 -->
```

**3. count - 计算关键词出现次数**
```html
{{text|count:"关键词"}}  <!-- 统计出现次数 -->
{{array|count:"值"}}  <!-- 统计数组中出现次数 -->
```

**4. split - 按空格拆分成数组**
```html
{{text|split}}  <!-- 按空格拆分 -->
{{text|split:","}}  <!-- 按逗号拆分 -->
```

**5. index - 获取关键词位置**
```html
{{text|index:"关键词"}}  <!-- 返回位置索引 -->
{{array|index:"值"}}  <!-- 返回数组中的位置 -->
```

**6. replace - 替换关键词**
```html
{{text|replace:"旧词,新词"}}  <!-- 替换文本 -->
{{text|replace:"CMS,内容管理系统"}}
```

**7. repeat - 重复输出字符串**
```html
{{text|repeat:3}}  <!-- 重复3次 -->
{{"*"|repeat:5}}  <!-- 输出: ***** -->
```

#### 数字和字符串运算过滤器

**8. add - 数字或字符串相加**
```html
{{number|add:10}}  <!-- 数字加法 -->
{{string|add:"后缀"}}  <!-- 字符串连接 -->
```

**9. addslashes - 添加反斜杠**
```html
{{text|addslashes}}  <!-- 转义特殊字符 -->
```

**10. upper/lower - 大小写转换**
```html
{{text|upper}}  <!-- 转大写 -->
{{text|lower}}  <!-- 转小写 -->
```

**11. center/ljust/rjust - 字符串对齐**
```html
{{text|center:20}}  <!-- 居中对齐，总长度20 -->
{{text|ljust:20}}   <!-- 左对齐 -->
{{text|rjust:20}}   <!-- 右对齐 -->
```

**12. cut - 移除指定字符**
```html
{{text|cut:" "}}  <!-- 移除所有空格 -->
{{text|cut:"-"}}  <!-- 移除所有横线 -->
```

#### 时间和日期过滤器

**13. date - 时间格式化**
```html
{{timestamp|date:"2006-01-02"}}  <!-- 格式化日期 -->
{{timestamp|date:"2006年01月02日 15:04:05"}}
```

#### 默认值和条件过滤器

**14. default - 设置默认值**
```html
{{value|default:"默认值"}}  <!-- 空值时显示默认值 -->
{{number|default:0}}  <!-- 数字默认值 -->
```

**15. divisibleby - 判断是否可被整除**
```html
{{number|divisibleby:2}}  <!-- 判断是否为偶数 -->
{% if forloop.Counter|divisibleby:3 %}每3个一组{% endif %}
```

**16. escape - 转义特殊字符**
```html
{{html|escape}}  <!-- 转义HTML字符 -->
{{js|escape}}    <!-- 转义JavaScript字符 -->
```

#### 数组和列表处理过滤器

**17. first/last - 获取首尾元素**
```html
{{array|first}}  <!-- 第一个元素 -->
{{array|last}}   <!-- 最后一个元素 -->
```

**18. floatformat - 浮点数格式化**
```html
{{price|floatformat:2}}  <!-- 保留2位小数 -->
{{number|floatformat}}   <!-- 自动格式化 -->
```

**19. get_digit - 获取指定位置数字**
```html
{{number|get_digit:1}}  <!-- 获取个位数 -->
{{number|get_digit:2}}  <!-- 获取十位数 -->
```

**20. int/float - 数字类型转换**
```html
{{string|int}}    <!-- 转换为整数 -->
{{string|float}}  <!-- 转换为浮点数 -->
```

**21. join - 连接数组元素**
```html
{{array|join:", "}}  <!-- 用逗号连接 -->
{{tags|join:" | "}}  <!-- 用竖线连接 -->
```

**22. length - 获取长度**
```html
{{string|length}}  <!-- 字符串长度 -->
{{array|length}}   <!-- 数组长度 -->
```

**23. linebreaks - 换行转HTML**
```html
{{text|linebreaks}}  <!-- 转换为<p>和<br>标签 -->
```

**24. phone2numeric - 手机键盘转数字**
```html
{{phone|phone2numeric}}  <!-- 将字母转为对应数字 -->
```

**25. pluralize - 复数形式**
```html
{{count}} item{{count|pluralize}}  <!-- 单复数处理 -->
{{count}} 个项目{{count|pluralize:"s"}}
```

**26. random - 随机选择**
```html
{{array|random}}  <!-- 随机选择一个元素 -->
{{string|random}} <!-- 随机选择一个字符 -->
```

**27. removetags - 移除HTML标签**
```html
{{html|removetags}}  <!-- 移除所有HTML标签 -->
{{html|removetags:"script,style"}}  <!-- 移除指定标签 -->
```

**28. safe - 安全输出HTML**
```html
{{html|safe}}  <!-- 不转义HTML内容 -->
```

**29. slice - 切片操作**
```html
{{string|slice:":5"}}   <!-- 前5个字符 -->
{{array|slice:"2:7"}}   <!-- 第2到第7个元素 -->
```

**30. split - 字符串分割**
```html
{{string|split:","}}  <!-- 按逗号分割成数组 -->
```

**31. stringformat - 格式化字符串**
```html
{{value|stringformat:"%d"}}  <!-- 格式化为整数 -->
{{value|stringformat:"%.2f"}} <!-- 格式化为2位小数 -->
```

**32. truncatechars - 按字符截取**
```html
{{text|truncatechars:100}}  <!-- 截取100个字符 -->
{{text|truncatechars:50:"..."}}  <!-- 自定义省略号 -->
```

**33. urlencode - URL编码**
```html
{{url|urlencode}}  <!-- URL参数编码 -->
```

**34. urlize - URL转链接**
```html
{{text|urlize}}  <!-- 将URL转为可点击链接 -->
```

**35. wordcount - 统计单词数量**
```html
{{text|wordcount}}  <!-- 统计英文单词数 -->
```

**36. wordwrap - 文本自动换行**
```html
{{text|wordwrap:80}}  <!-- 每80字符换行 -->
```

#### 逻辑运算过滤器

**37. and/or/not - 逻辑运算**
```html
{{value1|and:value2}}  <!-- 逻辑与 -->
{{value1|or:value2}}   <!-- 逻辑或 -->
{{value|not}}          <!-- 逻辑非 -->
```

#### 调试过滤器

**38. dump - 打印变量结构**
```html
{{variable|dump}}  <!-- 显示变量的类型和值 -->
```

#### 高级过滤器

**39. dictsort - 字典排序**
```html
{{objects|dictsort:"name"}}  <!-- 按name字段排序 -->
```

**40. yesno - 布尔值转换**
```html
{{boolean|yesno:"是,否"}}  <!-- true显示"是"，false显示"否" -->
{{boolean|yesno:"有,无,未知"}}  <!-- 三种状态 -->
```

### 过滤器使用技巧

**链式组合使用：**
```html
{{content|striptags|truncatechars:100|default:"暂无内容"}}
{{title|lower|title|truncatechars:50}}
{{price|floatformat:2|add:" 元"}}
```

**条件判断中使用：**
```html
{% if title|length > 20 %}
<span class="long-title">{{title|truncatechars:20}}</span>
{% else %}
<span>{{title}}</span>
{% endif %}
```

**循环中使用：**
```html
{% for item in items %}
{% if forloop.Counter|divisibleby:3 %}
<div class="row">
{% endif %}
<div class="col">{{item.Title|truncatechars:30}}</div>
{% if forloop.Counter|divisibleby:3 or forloop.Last %}
</div>
{% endif %}
{% endfor %}
```

## 通用模板标签相关

### 其他辅助标签

**说明：** 提供一些辅助功能的标签

**使用方法：**

```html
<!-- 获取当前时间戳 -->
{{now}}

<!-- 获取当前年份 -->
{{now|stampToDate:"2006"}}

<!-- 获取当前完整时间 -->
{{now|stampToDate:"2006-01-02 15:04:05"}}

<!-- 生成UUID -->
{{uuid}}

<!-- 获取随机数 -->
{{random}}
```

### 更多过滤器

**说明：** AnQiCMS提供了丰富的过滤器，除了在过滤器章节介绍的常用过滤器外，还有更多高级过滤器可以使用。

**高级过滤器示例：**

```html
<!-- 数组操作过滤器 -->
{{list|shuffle}}  <!-- 随机打乱数组 -->
{{list|reverse}}  <!-- 反转数组 -->
{{list|sort}}     <!-- 排序数组 -->

<!-- 字符串操作过滤器 -->
{{text|md5}}      <!-- MD5加密 -->
{{text|base64}}   <!-- Base64编码 -->
{{url|urlencode}} <!-- URL编码 -->

<!-- 数学运算过滤器 -->
{{number|abs}}    <!-- 绝对值 -->
{{number|round}}  <!-- 四舍五入 -->
{{price|currency}} <!-- 货币格式化 -->

<!-- 条件过滤器 -->
{{value|yesno:"是,否"}}  <!-- 布尔值转换 -->
{{list|dictsort:"name"}} <!-- 字典排序 -->
```

### 通用标签-定义变量赋值标签

**说明：** iris.Django模板引擎的模板解析器提供了可以在模板中声明变量并使用的方法with。通过with我们可以临时声明单个或多个变量，提供后续使用。

**使用方法：**

```html
<!-- 声明单个变量 -->
{% with title="这是声明给header使用的title" %}
{% include "header.html" %}
{% endwith %}

<!-- 声明多个变量 -->
{% with title="页面标题" keywords="关键词" description="页面描述" %}
{% include "header.html" %}
{% endwith %}

<!-- 使用变量赋值 -->
{% with total=items|length %}
共有 {{total}} 个项目
{% endwith %}
```

### 通用标签-if逻辑判断标签

**说明：** 标签判断表达式的值，如果表达式的值为 true 则执行其主体内容。

**使用方法：**

```html
<!-- 基本if判断 -->
{% if user %}
欢迎，{{user.name}}！
{% endif %}

<!-- if-else判断 -->
{% if archive.Thumb %}
<img src="{{archive.Thumb}}" alt="{{archive.Title}}" />
{% else %}
<img src="/static/images/default.jpg" alt="默认图片" />
{% endif %}

<!-- if-elif-else判断 -->
{% if archive.Views > 1000 %}
<span class="hot">热门</span>
{% elif archive.Views > 100 %}
<span class="popular">受欢迎</span>
{% else %}
<span class="normal">普通</span>
{% endif %}

<!-- 复杂条件判断 -->
{% if archive.Flag == "h" and archive.Views > 500 %}
<span class="featured">精选推荐</span>
{% endif %}

<!-- 包含判断 -->
{% if "推荐" in archive.Keywords %}
<span class="recommend">推荐</span>
{% endif %}
```

### 通用标签-for循环遍历标签

**说明：** for用于循环访问数组中的每个项目，从而使该项目在上下文变量中可用。

**使用方法：**

```html
<!-- 基本for循环 -->
{% for item in articles %}
<li class="item">
    <a href="/article/{{item.Id}}" class="link">{{item.Title}}</a>
</li>
{% endfor %}

<!-- 带empty的for循环 -->
{% for item in articles %}
<li>{{item.Title}}</li>
{% empty %}
<li>暂无文章</li>
{% endfor %}

<!-- 使用forloop变量 -->
{% for item in articles %}
<li class="{% if forloop.First %}first{% endif %} {% if forloop.Last %}last{% endif %}">
    <span>第{{forloop.Counter}}项，还剩{{forloop.Revcounter}}项</span>
    <a href="{{item.Link}}">{{item.Title}}</a>
</li>
{% endfor %}

<!-- 嵌套循环 -->
{% for category in categories %}
<div class="category">
    <h3>{{category.Title}}</h3>
    <ul>
    {% for article in category.Articles %}
    <li><a href="{{article.Link}}">{{article.Title}}</a></li>
    {% endfor %}
    </ul>
</div>
{% endfor %}
```

### 通用标签-算术运算标签

**说明：** 在模板中使用数学算术计算整数和复数表达式

**使用方法：**

```html
<!-- 基本运算 -->
{{ 10 + 5 }}     <!-- 输出: 15 -->
{{ 20 - 8 }}     <!-- 输出: 12 -->
{{ 6 * 7 }}      <!-- 输出: 42 -->
{{ 15 / 3 }}     <!-- 输出: 5 -->

<!-- 复杂运算 -->
{{ (10 + 5) * 2 }}           <!-- 输出: 30 -->
{{ -(10-100) }}              <!-- 输出: 90 -->
{{ -1 * (-(-(10-100))) }}    <!-- 输出: -90 -->

<!-- 与变量结合 -->
{{ archive.Views + 1 }}      <!-- 浏览量加1 -->
{{ archive.Price * 0.8 }}    <!-- 价格打8折 -->

<!-- 在条件中使用 -->
{% if archive.Views > 100 * 5 %}
<span>热门文章</span>
{% endif %}
```

### 格式化时间戳标签

**说明：** 模板中，支持直接对时间戳进行格式化成指定的格式化输出。

**使用方法：** `{{stampToDate(时间戳, "格式")}}`

时间戳为10位的时间，如 1609470335，格式为Golang支持的格式。

**格式说明：**
- `2006-01-02` 表示年-月-日
- `15:04:05` 表示时分秒
- `2006年01月02日` 表示中文格式

**代码示例：**

```html
<!-- 基本时间格式化 -->
{{stampToDate(item.CreatedTime, "2006-01-02")}}          <!-- 2021-06-30 -->
{{stampToDate(item.CreatedTime, "2006-01-02 15:04")}}    <!-- 2021-06-30 14:30 -->
{{stampToDate(item.CreatedTime, "2006年01月02日")}}        <!-- 2021年06月30日 -->

<!-- 在循环中使用 -->
{% archiveList archives with limit="10" %}
{% for item in archives %}
<div class="article">
    <h3>{{item.Title}}</h3>
    <span class="date">{{stampToDate(item.CreatedTime, "2006-01-02")}}</span>
</div>
{% endfor %}
{% endarchiveList %}

<!-- 与过滤器结合 -->
{{item.CreatedTime|stampToDate:"2006-01-02"|default:"未知时间"}}
```

### 通用标签-移除逻辑标签占用行

**说明：** 在模板中，逻辑标签（如if、for等）会占用行，有时我们希望移除这些空行以保持HTML的整洁。

**使用方法：** `{%- 标签名 -%}`

**代码示例：**

```html
<!-- 普通写法会产生空行 -->
{% if archive.Thumb %}
<img src="{{archive.Thumb}}" alt="{{archive.Title}}" />
{% endif %}

<!-- 使用-号移除空行 -->
{%- if archive.Thumb -%}
<img src="{{archive.Thumb}}" alt="{{archive.Title}}" />
{%- endif -%}

<!-- 循环中移除空行 -->
{%- for item in archives -%}
<li>{{item.Title}}</li>
{%- endfor -%}
```

### 模板文字翻译标签

**说明：** 用于多语言网站的文字翻译功能

**使用方法：** `{% trans "要翻译的文字" %}`

**代码示例：**

```html
<!-- 基本翻译 -->
<h1>{% trans "欢迎访问" %}</h1>
<p>{% trans "这是一个多语言网站" %}</p>

<!-- 带变量的翻译 -->
<span>{% trans "共有" %} {{total}} {% trans "篇文章" %}</span>

<!-- 复数形式翻译 -->
{% blocktrans count counter=list|length %}
有 {{ counter }} 个项目
{% plural %}
有 {{ counter }} 个项目
{% endblocktrans %}
```

### 获取多语言站点列表标签

**说明：** 用于获取多语言站点列表，在多语言网站中切换语言

**使用方法：** `{% siteList 变量名称 %}`

如将变量定义为 sites `{% siteList sites %}...{% endsiteList %}`

**sites 是一个数组对象，item 为 for 循环体内的变量，可用的字段有：**

- 站点ID `Id`
- 站点名称 `SiteName`
- 站点域名 `BaseUrl`
- 语言代码 `Language`
- 是否当前站点 `IsCurrent`

**代码示例：**

```html
<!-- 语言切换菜单 -->
{% siteList sites %}
<div class="language-switcher">
    {% for site in sites %}
    <a href="{{site.BaseUrl}}" class="{% if site.IsCurrent %}active{% endif %}">
        {{site.Language}}
    </a>
    {% endfor %}
</div>
{% endsiteList %}

<!-- 下拉语言选择 -->
{% siteList sites %}
<select onchange="location.href=this.value">
    {% for site in sites %}
    <option value="{{site.BaseUrl}}" {% if site.IsCurrent %}selected{% endif %}>
        {{site.SiteName}}
    </option>
    {% endfor %}
</select>
{% endsiteList %}
```

### 生成随机文本

**说明：** lorem 标签可以生成指定长度的随机文本内容。通常用于排版需要。

**使用方法：** `{% lorem 数量 方法 random %}`

- 数量可以是按字数，也可以是按单词数计算，需要根据方法来决定
- 方法为以下几种中的一种：'w', 'p' or 'b'
- random 为是否随机，默认不填，填写了random则随机生成

**代码示例：**

```html
<!-- 生成5个单词 -->
{% lorem 5 w %}

<!-- 生成2个段落 -->
{% lorem 2 p %}

<!-- 生成1个块 -->
{% lorem 1 b %}

<!-- 随机生成3个单词 -->
{% lorem 3 w random %}
```

### 模板的嵌套引用 include

**说明：** 往往制作模板的时候，我们会将一些公共部分，比如header、footer、aside等部分，抽离出来独立存放，不需要在每一个页面都重复编写，只需要在每一个页面引入它们即可。

**使用方法：**

```html
<!-- 基本引用 -->
{% include "header.html" %}
{% include "footer.html" %}

<!-- 带变量引用 -->
{% with title="首页" %}
{% include "header.html" %}
{% endwith %}

<!-- 引用时传递多个变量 -->
{% include "sidebar.html" with title="侧边栏" show_ads=true %}

<!-- 条件引用 -->
{% if user.is_authenticated %}
{% include "user_menu.html" %}
{% else %}
{% include "guest_menu.html" %}
{% endif %}
```

---

## 模板开发最佳实践

### 1. 文件结构建议

```
template/
├── index.html          # 首页模板
├── archive.html        # 文档列表页模板
├── detail.html         # 文档详情页模板
├── category.html       # 分类页模板
├── page.html          # 单页模板
├── tag.html           # 标签页模板
├── search.html        # 搜索页模板
├── partials/          # 公共组件目录
│   ├── header.html    # 头部组件
│   ├── footer.html    # 底部组件
│   ├── sidebar.html   # 侧边栏组件
│   └── nav.html       # 导航组件
├── static/            # 静态资源目录
│   ├── css/
│   ├── js/
│   └── images/
└── config.json        # 模板配置文件
```

### 2. 常用代码片段

#### 响应式图片处理
```html
{% if archive.Thumb %}
<img src="{{archive.Thumb}}" alt="{{archive.Title}}" loading="lazy" />
{% else %}
<img src="/static/images/default.jpg" alt="默认图片" loading="lazy" />
{% endif %}
```

#### 分页导航
```html
{% pagination pages with show="5" %}
<nav class="pagination">
{% for page in pages %}
{% if page.IsCurrent %}
<span class="current">{{page.Name}}</span>
{% else %}
<a href="{{page.Link}}">{{page.Name}}</a>
{% endif %}
{% endfor %}
</nav>
{% endpagination %}
```

#### SEO优化的TDK设置
```html
<title>{% tdk with name="Title" %}</title>
<meta name="keywords" content="{% tdk with name="Keywords" %}" />
<meta name="description" content="{% tdk with name="Description" %}" />
<link rel="canonical" href="{% archiveDetail with name="CanonicalUrl" %}" />
```

### 3. 性能优化建议

1. **图片懒加载**：使用 `lazy="data-src"` 参数
2. **内容缓存**：合理使用静态化功能
3. **代码压缩**：压缩CSS和JavaScript文件
4. **CDN加速**：静态资源使用CDN

### 4. 常见问题解决

#### 问题1：图片不显示
```html
<!-- 错误写法 -->
<img src="{{archive.Logo}}" />

<!-- 正确写法 -->
{% if archive.Logo %}
<img src="{{archive.Logo}}" alt="{{archive.Title}}" />
{% endif %}
```

#### 问题2：时间格式化错误
```html
<!-- 错误写法 -->
{{archive.CreatedTime}}

<!-- 正确写法 -->
{{stampToDate(archive.CreatedTime, "2006-01-02")}}
```

#### 问题3：HTML内容不显示
```html
<!-- 错误写法 -->
{{archive.Content}}

<!-- 正确写法 -->
{{archive.Content|safe}}
```

### 5. 调试技巧

1. **使用开发者工具**：检查HTML结构和CSS样式
2. **查看源码**：确认标签是否正确输出
3. **逐步调试**：先输出变量内容，再进行格式化
4. **使用条件判断**：避免空值导致的错误

### 6. 模板兼容性

- 确保模板在不同浏览器中的兼容性
- 测试移动端显示效果
- 验证SEO标签的正确性
- 检查无障碍访问支持

---

## 高级功能和技巧

### 1. 模板缓存优化

**说明：** 合理使用缓存可以显著提升网站性能

```html
<!-- 缓存文档列表 -->
{% cache 300 "archive_list" category.Id %}
{% archiveList archives with categoryId=category.Id limit="10" %}
{% for item in archives %}
<div class="article-item">
    <h3><a href="{{item.Link}}">{{item.Title}}</a></h3>
</div>
{% endfor %}
{% endarchiveList %}
{% endcache %}
```

### 2. 条件加载和懒加载

**说明：** 根据条件动态加载内容，提升用户体验

```html
<!-- 条件加载侧边栏 -->
{% if request.is_mobile %}
{% include "mobile_sidebar.html" %}
{% else %}
{% include "desktop_sidebar.html" %}
{% endif %}

<!-- 图片懒加载 -->
{% archiveDetail with name="Content" lazy="data-src" %}
```

### 3. 自定义过滤器组合

**说明：** 多个过滤器组合使用，实现复杂的数据处理

```html
<!-- 复杂的文本处理 -->
{{archive.Content|striptags|truncatechars:200|linebreaksbr|safe}}

<!-- 时间和默认值组合 -->
{{archive.CreatedTime|stampToDate:"2006年01月02日"|default:"时间未知"}}

<!-- 数组处理组合 -->
{{tags|slice:":5"|join:", "|default:"暂无标签"}}
```

### 4. 响应式设计支持

**说明：** 根据设备类型显示不同内容

```html
<!-- 响应式图片 -->
{% if archive.Images %}
<picture>
    <source media="(max-width: 768px)" srcset="{{archive.Images.0|resize:'400x300'}}">
    <source media="(max-width: 1200px)" srcset="{{archive.Images.0|resize:'800x600'}}">
    <img src="{{archive.Images.0}}" alt="{{archive.Title}}" loading="lazy">
</picture>
{% endif %}

<!-- 移动端专用内容 -->
{% if request.is_mobile %}
<div class="mobile-only">
    <!-- 移动端专用内容 -->
</div>
{% endif %}
```

### 5. SEO优化技巧

**说明：** 提升搜索引擎优化效果

```html
<!-- 结构化数据 -->
{% jsonLd %}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "{% archiveDetail with name='Title' %}",
    "image": "{% archiveDetail with name='Logo' %}",
    "author": {
        "@type": "Person",
        "name": "{% archiveDetail with name='Author' %}"
    },
    "datePublished": "{% archiveDetail with name='CreatedTime' format='2006-01-02T15:04:05Z' %}",
    "dateModified": "{% archiveDetail with name='UpdatedTime' format='2006-01-02T15:04:05Z' %}"
}
</script>
{% endjsonLd %}

<!-- 面包屑导航 -->
{% breadcrumb crumbs with index="首页" %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb" itemscope itemtype="https://schema.org/BreadcrumbList">
    {% for item in crumbs %}
    <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
        {% if not forloop.Last %}
        <a href="{{item.Link}}" itemprop="item">
            <span itemprop="name">{{item.Title}}</span>
        </a>
        {% else %}
        <span itemprop="name">{{item.Title}}</span>
        {% endif %}
        <meta itemprop="position" content="{{forloop.Counter}}" />
    </li>
    {% endfor %}
    </ol>
</nav>
{% endbreadcrumb %}
```

### 6. 多语言支持

**说明：** 构建多语言网站的最佳实践

```html
<!-- 语言切换器 -->
{% siteList sites %}
<div class="language-switcher">
    <button class="dropdown-toggle">
        {% for site in sites %}
        {% if site.IsCurrent %}{{site.Language}}{% endif %}
        {% endfor %}
    </button>
    <ul class="dropdown-menu">
    {% for site in sites %}
    <li>
        <a href="{{site.BaseUrl}}" hreflang="{{site.Language}}">
            {{site.SiteName}}
        </a>
    </li>
    {% endfor %}
    </ul>
</div>
{% endsiteList %}

<!-- 多语言内容 -->
<h1>{% trans "欢迎访问我们的网站" %}</h1>
<p>{% trans "这里有最新的资讯和产品信息" %}</p>
```

## 常见问题和解决方案

### Q1: 如何处理空数据？

```html
<!-- 使用default过滤器 -->
{{archive.Description|default:"暂无描述"}}

<!-- 使用条件判断 -->
{% if archive.Thumb %}
<img src="{{archive.Thumb}}" alt="{{archive.Title}}" />
{% else %}
<div class="no-image">暂无图片</div>
{% endif %}
```

### Q2: 如何实现分页？

```html
<!-- 完整的分页实现 -->
{% pagination pages with show="5" %}
<nav class="pagination-wrapper">
    {% if pages.PrevPage %}
    <a href="{{pages.PrevPage.Link}}" class="prev">上一页</a>
    {% endif %}

    {% for page in pages.Pages %}
    {% if page.IsCurrent %}
    <span class="current">{{page.Name}}</span>
    {% else %}
    <a href="{{page.Link}}">{{page.Name}}</a>
    {% endif %}
    {% endfor %}

    {% if pages.NextPage %}
    <a href="{{pages.NextPage.Link}}" class="next">下一页</a>
    {% endif %}
</nav>
{% endpagination %}
```

### Q3: 如何优化加载速度？

```html
<!-- 图片懒加载 -->
{% archiveDetail with name="Content" lazy="data-src" %}

<!-- 关键CSS内联 -->
<style>
/* 关键CSS */
.header { display: flex; }
</style>

<!-- 非关键CSS异步加载 -->
<link rel="preload" href="/static/css/main.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
```

## 更新日志

**2025年1月17日更新（第二次）：**
- 新增了模板制作基础章节，包含：
  - 模板制作的基本约定（语法规则、命名规则、编码要求）
  - 模板目录结构和配置（config.json配置、目录组织模式）
  - 模板标签概览（38种内置标签分类和使用示例）
- 补充了完整的40个过滤器详细说明和使用方法
- 完善了Tag文档列表标签和Tag详情标签的参数说明
- 添加了更多实用的代码示例和最佳实践
- 基于3个新增官方URL完善了模板开发基础知识

**2025年1月17日更新（第一次）：**
- 添加了Json-LD自定义调用标签详细说明
- 补充了首页Banner列表标签的完整文档
- 新增了统计代码标签和文档模型详情标签
- 添加了用户相关标签和自定义内容标签
- 补充了通用模板标签的缺失部分
- 增加了高级功能和技巧章节
- 添加了常见问题和解决方案
- 根据官方文档URL目录完善了所有标签说明

**参考资源：**
- [AnQiCMS官方文档](https://www.anqicms.com/manual)
- [完整URL目录](AnQiCMS_Complete_Real_URLs.md)
- [模板制作的基本约定](https://www.anqicms.com/help-design/116.html)
- [模板制作的目录和模板](https://www.anqicms.com/help-design/117.html)
- [模板的标签和使用方法](https://www.anqicms.com/help-design/118.html)

---

## 总结

AnQiCMS提供了丰富的模板标签和过滤器，能够满足各种网站开发需求。通过合理使用这些标签，可以快速构建功能完善、性能优良的网站模板。

**主要特点：**
- 标签语法简洁明了
- 支持复杂的逻辑判断和循环
- 丰富的过滤器支持
- 良好的扩展性和兼容性

**开发建议：**
- 熟练掌握常用标签的使用方法
- 合理组织模板文件结构
- 注重SEO优化和性能优化
- 保持代码的可维护性

希望这份文档能够帮助您更好地进行AnQiCMS模板开发！

**文档特色：**
- 📚 基于官方文档URL目录完整更新，包含模板制作基础知识
- 🔗 包含89个真实有效的官方链接（新增3个模板制作基础链接）
- 💡 提供丰富的代码示例和最佳实践（100+个实用示例）
- 🚀 涵盖高级功能和性能优化技巧
- 🌐 支持多语言和SEO优化指导
- ❓ 包含常见问题和解决方案
- 🏗️ 详细的模板目录结构和配置说明
- 🔧 完整的40个过滤器使用指南

如有疑问或需要更多帮助，请参考[AnQiCMS官方文档](https://www.anqicms.com/manual)或查看[完整URL目录](AnQiCMS_Complete_Real_URLs.md)。
