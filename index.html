<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U-StuDio - 未来科技工作室,重塑创意边界!</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        /* 自定义渐变背景 */
        .bg-main-gradient {
            background: linear-gradient(135deg, #000000 0%, #0a0e1a 50%, #000000 100%);
        }
        
        /* 科技渐变色 */
        .text-tech-gradient {
            background: linear-gradient(135deg, #00d4ff 0%, #8b5fbf 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* 毛玻璃效果 */
        .glass-card {
            background: rgba(26, 31, 58, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        /* 悬浮发光效果 */
        .glow-hover:hover {
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
            transform: translateY(-5px);
            border-color: rgba(0, 212, 255, 0.8);
        }
        
        /* 按钮渐变 */
        .btn-primary {
            background: linear-gradient(135deg, #00f5ff 0%, #00d4ff 100%);
        }
        
        .btn-secondary {
            background: transparent;
            border: 2px solid;
            border-image: linear-gradient(135deg, #00d4ff, #8b5fbf) 1;
        }
        
        /* 粒子动画背景 */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00d4ff;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
        
        /* 打字机效果 */
        .typewriter {
            overflow: hidden;
            border-right: 2px solid #00f5ff;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }
        
        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }
        
        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: #00f5ff }
        }
        
        /* 滚动动画 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }
        
        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 视差效果 */
        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }
    </style>
</head>
<body class="bg-main-gradient text-white overflow-x-hidden">
    
    <!-- 导航栏 -->
    <nav class="fixed top-0 w-full z-50 glass-card transition-all duration-300" id="navbar">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-tech-gradient">
                    U-STUDIO
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="hover:text-cyan-400 transition-colors">首页</a>
                    <a href="#services" class="hover:text-cyan-400 transition-colors">服务</a>
                    <a href="#studio" class="hover:text-cyan-400 transition-colors">影棚</a>
                    <a href="#works" class="hover:text-cyan-400 transition-colors">作品</a>
                    <a href="#tech" class="hover:text-cyan-400 transition-colors">技术</a>
                    <a href="#pricing" class="hover:text-cyan-400 transition-colors">价格</a>
                    <a href="#about" class="hover:text-cyan-400 transition-colors">关于</a>
                </div>
                <button class="btn-primary px-6 py-2 rounded-full text-black font-semibold hover:scale-105 transition-transform">
                    立即预约
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero区域 -->
    <section id="home" class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- 粒子背景 -->
        <div class="particles">
            <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
            <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
            <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
            <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
            <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
            <div class="particle" style="left: 70%; animation-delay: 0.5s;"></div>
            <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
            <div class="particle" style="left: 90%; animation-delay: 2.5s;"></div>
        </div>
        
        <!-- 背景视频效果 -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-black/40"></div>
        <div class="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')] bg-cover bg-center opacity-30"></div>
        
        <!-- Hero内容 -->
        <div class="relative z-10 text-center px-6 max-w-6xl mx-auto">
            <h1 class="text-6xl md:text-8xl font-bold mb-6 typewriter">
                <span class="text-tech-gradient">重塑创意边界</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-300 max-w-3xl mx-auto">
                AI科技融合 × 800平专业影棚 × 无限创意可能
            </p>
            <p class="text-lg mb-12 text-gray-400 max-w-2xl mx-auto">
                专业拍摄、场地租赁、AIGC服务、融合制作四大核心业务<br>
                为您提供一站式视觉内容解决方案
            </p>
            
            <!-- CTA按钮 -->
            <div class="flex justify-center mb-16">
                <button class="btn-primary px-12 py-4 rounded-full text-black font-bold text-lg hover:scale-105 transition-transform">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    立即预约
                </button>
            </div>
        </div>
    </section>

    <!-- 服务概览 -->
    <section id="services" class="py-20 px-6">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">核心服务</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    四大核心服务板块，满足您的所有视觉内容需求
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- 专业拍摄 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-camera text-3xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-white">专业拍摄</h3>
                        <p class="text-gray-400 mb-6">商业摄影、视频制作、直播支持，专业团队为您提供高质量拍摄服务</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>商业广告拍摄</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>产品展示视频</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>直播技术支持</li>
                        </ul>
                    </div>
                </div>

                <!-- 场地租赁 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-purple-400 to-pink-600 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-building text-3xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-white">场地租赁</h3>
                        <p class="text-gray-400 mb-6">800平超大影棚，专业设备齐全，灯光音响一应俱全</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>800平超大空间</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>专业灯光系统</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>高端音响设备</li>
                        </ul>
                    </div>
                </div>

                <!-- AIGC服务 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-400 to-teal-600 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-robot text-3xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-white">AIGC服务</h3>
                        <p class="text-gray-400 mb-6">AI生图、视频生成、后期增强，前沿技术助力创意实现</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>AI智能生图</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>视频智能生成</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>后期AI增强</li>
                        </ul>
                    </div>
                </div>

                <!-- 融合制作 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-orange-400 to-red-600 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-magic text-3xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-white">融合制作</h3>
                        <p class="text-gray-400 mb-6">实拍+AI结合，虚实融合创作，打造独一无二的视觉效果</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>实拍AI融合</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>虚实结合创作</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>创意效果制作</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 影棚实景展示 -->
    <section id="studio" class="py-20 px-6 bg-gradient-to-b from-transparent to-blue-900/10">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">800平专业影棚</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    分区域专业设计，设备齐全，满足各种拍摄需求
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
                <!-- 拍摄区 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-4 text-white">专业拍摄区</h3>
                        <p class="text-gray-400 mb-4">400平主拍摄区域，配备专业摄像设备和灯光系统</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>4K专业摄像机</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>环形灯光系统</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>绿幕背景墙</li>
                        </ul>
                    </div>
                </div>

                <!-- 休息区 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-4 text-white">舒适休息区</h3>
                        <p class="text-gray-400 mb-4">200平休息区域，为团队提供舒适的工作环境</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>现代化休息室</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>茶水间设施</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>会议讨论区</li>
                        </ul>
                    </div>
                </div>

                <!-- 后期制作区 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-4 text-white">后期制作区</h3>
                        <p class="text-gray-400 mb-4">200平后期制作区域，配备高端工作站和专业软件</p>
                        <ul class="text-sm text-gray-500 space-y-2">
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>高性能工作站</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>专业调色设备</li>
                            <li><i class="fas fa-check text-cyan-400 mr-2"></i>AIGC处理系统</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 设备亮点 -->
            <div class="glass-card p-8 rounded-3xl scroll-reveal">
                <h3 class="text-3xl font-bold mb-8 text-center text-tech-gradient">专业设备配置</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <i class="fas fa-video text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">摄像设备</h4>
                        <p class="text-sm text-gray-400">RED、ARRI专业摄像机</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-lightbulb text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">灯光系统</h4>
                        <p class="text-sm text-gray-400">LED环形灯、柔光箱</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-volume-up text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">音响设备</h4>
                        <p class="text-sm text-gray-400">专业录音、监听系统</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-desktop text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">后期设备</h4>
                        <p class="text-sm text-gray-400">高端工作站、调色台</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 精选作品展示 -->
    <section id="works" class="py-20 px-6">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">精选作品</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    传统制作与AIGC技术完美融合，创造无限可能
                </p>
            </div>

            <!-- 分类筛选 -->
            <div class="flex flex-wrap justify-center gap-4 mb-12 scroll-reveal">
                <button class="px-6 py-3 rounded-full glass-card text-cyan-400 border-cyan-400 border">全部作品</button>
                <button class="px-6 py-3 rounded-full glass-card hover:text-cyan-400 transition-colors">商业广告</button>
                <button class="px-6 py-3 rounded-full glass-card hover:text-cyan-400 transition-colors">电商直播</button>
                <button class="px-6 py-3 rounded-full glass-card hover:text-cyan-400 transition-colors">短视频</button>
                <button class="px-6 py-3 rounded-full glass-card hover:text-cyan-400 transition-colors">艺术创作</button>
            </div>

            <!-- 作品网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 作品1 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="商业广告作品" class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-cyan-500 text-black px-3 py-1 rounded-full text-sm font-semibold">商业广告</span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">AIGC增强</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">科技产品发布会</h3>
                        <p class="text-gray-400 text-sm mb-4">实拍+AI背景生成，打造未来科技感</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">查看详情</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 作品2 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="电商直播作品" class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-500 text-black px-3 py-1 rounded-full text-sm font-semibold">电商直播</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">时尚品牌直播间</h3>
                        <p class="text-gray-400 text-sm mb-4">专业灯光设计，提升产品展示效果</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">查看详情</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 作品3 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="短视频作品" class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-orange-500 text-black px-3 py-1 rounded-full text-sm font-semibold">短视频</span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">AI生成</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">创意短视频系列</h3>
                        <p class="text-gray-400 text-sm mb-4">AI辅助创意，快速生成多样化内容</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">查看详情</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 作品4 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="艺术创作" class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-pink-500 text-black px-3 py-1 rounded-full text-sm font-semibold">艺术创作</span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">虚实融合</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">数字艺术装置</h3>
                        <p class="text-gray-400 text-sm mb-4">实体拍摄与数字艺术的完美结合</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">查看详情</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 作品5 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="商业广告作品" class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-cyan-500 text-black px-3 py-1 rounded-full text-sm font-semibold">商业广告</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">企业形象宣传片</h3>
                        <p class="text-gray-400 text-sm mb-4">专业团队打造，展现企业实力</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">查看详情</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 作品6 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="AIGC作品" class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold">AIGC</span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-green-500 text-black px-3 py-1 rounded-full text-sm">AI生成</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">AI概念视频</h3>
                        <p class="text-gray-400 text-sm mb-4">纯AI生成，展现未来可能性</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">查看详情</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 查看更多 -->
            <div class="text-center mt-12 scroll-reveal">
                <button class="btn-secondary px-8 py-4 rounded-full text-white font-bold hover:scale-105 transition-transform">
                    查看更多作品
                    <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- 技术实力展示 -->
    <section id="tech" class="py-20 px-6 bg-gradient-to-b from-blue-900/10 to-purple-900/10">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">技术实力</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    前沿技术与专业设备的完美结合，为您提供顶级制作体验
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-16">
                <!-- 硬件设备 -->
                <div class="scroll-reveal">
                    <h3 class="text-3xl font-bold mb-8 text-tech-gradient">硬件设备</h3>
                    <div class="grid grid-cols-2 gap-6">
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <i class="fas fa-video text-3xl text-cyan-400 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">专业摄像设备</h4>
                            <p class="text-sm text-gray-400">RED、ARRI、Sony FX系列专业摄像机</p>
                        </div>
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <i class="fas fa-lightbulb text-3xl text-cyan-400 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">灯光音响系统</h4>
                            <p class="text-sm text-gray-400">LED环形灯、柔光箱、专业音响</p>
                        </div>
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <i class="fas fa-desktop text-3xl text-cyan-400 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">后期制作设备</h4>
                            <p class="text-sm text-gray-400">高端工作站、专业调色台</p>
                        </div>
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <i class="fas fa-microchip text-3xl text-cyan-400 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">AI计算集群</h4>
                            <p class="text-sm text-gray-400">高性能GPU集群，支持实时AI处理</p>
                        </div>
                    </div>
                </div>

                <!-- AIGC技术 -->
                <div class="scroll-reveal">
                    <h3 class="text-3xl font-bold mb-8 text-tech-gradient">AIGC技术</h3>
                    <div class="space-y-6">
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-image text-2xl text-cyan-400 mr-4"></i>
                                <h4 class="text-lg font-semibold">AI图像生成</h4>
                            </div>
                            <p class="text-gray-400 mb-3">Stable Diffusion、Midjourney等先进模型</p>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-cyan-400 to-blue-600 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-film text-2xl text-cyan-400 mr-4"></i>
                                <h4 class="text-lg font-semibold">AI视频生成</h4>
                            </div>
                            <p class="text-gray-400 mb-3">RunwayML、Pika Labs等视频生成技术</p>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-cyan-400 to-purple-600 h-2 rounded-full" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-magic text-2xl text-cyan-400 mr-4"></i>
                                <h4 class="text-lg font-semibold">后期AI增强</h4>
                            </div>
                            <p class="text-gray-400 mb-3">智能抠图、风格转换、画质增强</p>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-cyan-400 to-green-600 h-2 rounded-full" style="width: 98%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据统计 -->
            <div class="glass-card p-8 rounded-3xl scroll-reveal">
                <h3 class="text-3xl font-bold mb-8 text-center text-tech-gradient">技术数据</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyan-400 mb-2">500+</div>
                        <div class="text-gray-400">项目完成</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyan-400 mb-2">99.5%</div>
                        <div class="text-gray-400">客户满意度</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyan-400 mb-2">90%</div>
                        <div class="text-gray-400">效率提升</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyan-400 mb-2">24/7</div>
                        <div class="text-gray-400">技术支持</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格方案 -->
    <section id="pricing" class="py-20 px-6">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">价格方案</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    透明化定价，多种套餐选择，满足不同需求
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <!-- 基础套餐 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">基础套餐</h3>
                        <div class="text-4xl font-bold text-tech-gradient mb-6">¥2,999<span class="text-lg text-gray-400">/天</span></div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>影棚租赁（8小时）</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>基础灯光设备</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>摄影师1名</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>基础后期处理</li>
                            <li class="flex items-center"><i class="fas fa-times text-gray-500 mr-3"></i>AIGC服务</li>
                        </ul>
                        <button class="w-full btn-secondary py-3 rounded-full font-semibold hover:scale-105 transition-transform">
                            选择套餐
                        </button>
                    </div>
                </div>

                <!-- 进阶套餐 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal border-2 border-cyan-400 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-cyan-400 text-black px-4 py-2 rounded-full text-sm font-bold">推荐</span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">进阶套餐</h3>
                        <div class="text-4xl font-bold text-tech-gradient mb-6">¥5,999<span class="text-lg text-gray-400">/天</span></div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>影棚租赁（12小时）</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>专业灯光音响</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>摄影团队3名</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>专业后期制作</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>基础AIGC服务</li>
                        </ul>
                        <button class="w-full btn-primary py-3 rounded-full font-semibold text-black hover:scale-105 transition-transform">
                            选择套餐
                        </button>
                    </div>
                </div>

                <!-- 定制方案 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">定制方案</h3>
                        <div class="text-4xl font-bold text-tech-gradient mb-6">面议</div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>个性化定制</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>全套设备支持</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>专业团队配置</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>高端后期制作</li>
                            <li class="flex items-center"><i class="fas fa-check text-cyan-400 mr-3"></i>全套AIGC服务</li>
                        </ul>
                        <button class="w-full btn-secondary py-3 rounded-full font-semibold hover:scale-105 transition-transform">
                            咨询定制
                        </button>
                    </div>
                </div>
            </div>

            <!-- 在线估价器 -->
            <div class="glass-card p-8 rounded-3xl scroll-reveal">
                <h3 class="text-2xl font-bold mb-6 text-center">在线估价器</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">拍摄类型</label>
                        <select class="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 text-white">
                            <option>商业广告</option>
                            <option>电商直播</option>
                            <option>短视频</option>
                            <option>艺术创作</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">拍摄时长</label>
                        <select class="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 text-white">
                            <option>半天（4小时）</option>
                            <option>全天（8小时）</option>
                            <option>加班（12小时）</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">AIGC需求</label>
                        <select class="w-full p-3 rounded-lg bg-gray-800 border border-gray-600 text-white">
                            <option>不需要</option>
                            <option>基础AI处理</option>
                            <option>高级AI制作</option>
                        </select>
                    </div>
                </div>
                <div class="text-center mt-6">
                    <button class="btn-primary px-8 py-3 rounded-full text-black font-semibold hover:scale-105 transition-transform">
                        获取报价
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户案例 -->
    <section id="clients" class="py-20 px-6 bg-gradient-to-b from-purple-900/10 to-transparent">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">客户案例</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    与知名品牌深度合作，共创视觉传奇
                </p>
            </div>

            <!-- 知名客户Logo墙 -->
            <div class="mb-16 scroll-reveal">
                <h3 class="text-2xl font-bold mb-8 text-center">合作伙伴</h3>
                <div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
                    <div class="glass-card p-6 rounded-2xl w-32 h-20 flex items-center justify-center">
                        <span class="text-lg font-bold">华为</span>
                    </div>
                    <div class="glass-card p-6 rounded-2xl w-32 h-20 flex items-center justify-center">
                        <span class="text-lg font-bold">小米</span>
                    </div>
                    <div class="glass-card p-6 rounded-2xl w-32 h-20 flex items-center justify-center">
                        <span class="text-lg font-bold">阿里巴巴</span>
                    </div>
                    <div class="glass-card p-6 rounded-2xl w-32 h-20 flex items-center justify-center">
                        <span class="text-lg font-bold">腾讯</span>
                    </div>
                    <div class="glass-card p-6 rounded-2xl w-32 h-20 flex items-center justify-center">
                        <span class="text-lg font-bold">字节跳动</span>
                    </div>
                    <div class="glass-card p-6 rounded-2xl w-32 h-20 flex items-center justify-center">
                        <span class="text-lg font-bold">美团</span>
                    </div>
                </div>
            </div>

            <!-- 成功案例详情 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
                <!-- 案例1 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="华为" class="w-16 h-16 rounded-full mr-4">
                        <div>
                            <h3 class="text-xl font-bold">华为Mate系列发布会</h3>
                            <p class="text-gray-400">科技产品发布</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6">
                        为华为Mate系列新品发布会提供全套视觉制作服务，结合AIGC技术打造未来科技感的宣传视频，获得了极佳的市场反响。
                    </p>
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400">1000万+</div>
                            <div class="text-sm text-gray-400">播放量</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400">95%</div>
                            <div class="text-sm text-gray-400">好评率</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400">30天</div>
                            <div class="text-sm text-gray-400">制作周期</div>
                        </div>
                    </div>
                    <blockquote class="border-l-4 border-cyan-400 pl-4 italic text-gray-300">
                        "U-STUDIO的专业水准和创新技术让我们的产品发布会达到了前所未有的视觉效果。"
                    </blockquote>
                </div>

                <!-- 案例2 -->
                <div class="glass-card p-8 rounded-3xl glow-hover transition-all duration-300 scroll-reveal">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="阿里巴巴" class="w-16 h-16 rounded-full mr-4">
                        <div>
                            <h3 class="text-xl font-bold">阿里巴巴双11大促</h3>
                            <p class="text-gray-400">电商直播制作</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6">
                        为阿里巴巴双11购物节打造多个品牌的直播间设计和内容制作，通过专业的灯光设计和AI辅助制作，显著提升了转化率。
                    </p>
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400">50+</div>
                            <div class="text-sm text-gray-400">直播间</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400">200%</div>
                            <div class="text-sm text-gray-400">转化提升</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400">15天</div>
                            <div class="text-sm text-gray-400">执行周期</div>
                        </div>
                    </div>
                    <blockquote class="border-l-4 border-cyan-400 pl-4 italic text-gray-300">
                        "专业的影棚环境和创新的制作方式，让我们的直播效果超出预期，销售转化率大幅提升。"
                    </blockquote>
                </div>
            </div>
        </div>
    </section>

    <!-- 资讯动态 -->
    <section id="news" class="py-20 px-6">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">资讯动态</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    关注行业前沿，分享技术洞察
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 资讯1 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="AIGC技术趋势" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-cyan-500 text-black px-3 py-1 rounded-full text-sm font-semibold mr-3">技术趋势</span>
                            <span class="text-gray-400 text-sm">2024-01-15</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3">2024年AIGC技术发展趋势预测</h3>
                        <p class="text-gray-400 text-sm mb-4">深度解析人工智能生成内容技术的最新发展方向，探讨其在影视制作领域的应用前景...</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">阅读全文</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 资讯2 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <img src="https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="影棚升级" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-green-500 text-black px-3 py-1 rounded-full text-sm font-semibold mr-3">公司动态</span>
                            <span class="text-gray-400 text-sm">2024-01-10</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3">影棚设备全面升级完成</h3>
                        <p class="text-gray-400 text-sm mb-4">我们的800平影棚完成了新一轮设备升级，引入了最新的8K摄像设备和AI处理系统...</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">阅读全文</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 资讯3 -->
                <div class="glass-card rounded-3xl overflow-hidden glow-hover transition-all duration-300 scroll-reveal">
                    <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="制作技巧" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">教程分享</span>
                            <span class="text-gray-400 text-sm">2024-01-05</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3">AI辅助视频制作完全指南</h3>
                        <p class="text-gray-400 text-sm mb-4">从前期策划到后期制作，详细介绍如何利用AI技术提升视频制作效率和质量...</p>
                        <div class="flex items-center justify-between">
                            <span class="text-cyan-400 text-sm">阅读全文</span>
                            <i class="fas fa-arrow-right text-cyan-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 查看更多 -->
            <div class="text-center mt-12 scroll-reveal">
                <button class="btn-secondary px-8 py-4 rounded-full text-white font-bold hover:scale-105 transition-transform">
                    查看更多资讯
                    <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="py-20 px-6 bg-gradient-to-b from-blue-900/10 to-transparent">
        <div class="container mx-auto max-w-7xl">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-5xl font-bold mb-6 text-tech-gradient">关于我们</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    专业团队，前沿技术，为您打造无限可能
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-16">
                <!-- 公司介绍 -->
                <div class="scroll-reveal">
                    <h3 class="text-3xl font-bold mb-6 text-tech-gradient">公司简介</h3>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        U-STUDIO成立于2020年，是国内领先的AI与传统影视制作融合服务商。我们拥有800平方米的专业影棚，配备最先进的拍摄设备和AI处理系统。
                    </p>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        我们的核心团队由资深影视制作专家和AI技术专家组成，致力于为客户提供从创意策划到后期制作的一站式视觉内容解决方案。
                    </p>
                    <div class="grid grid-cols-2 gap-6">
                        <div class="glass-card p-4 rounded-2xl">
                            <div class="text-2xl font-bold text-cyan-400 mb-2">2020</div>
                            <div class="text-gray-400">成立年份</div>
                        </div>
                        <div class="glass-card p-4 rounded-2xl">
                            <div class="text-2xl font-bold text-cyan-400 mb-2">50+</div>
                            <div class="text-gray-400">团队成员</div>
                        </div>
                        <div class="glass-card p-4 rounded-2xl">
                            <div class="text-2xl font-bold text-cyan-400 mb-2">500+</div>
                            <div class="text-gray-400">服务客户</div>
                        </div>
                        <div class="glass-card p-4 rounded-2xl">
                            <div class="text-2xl font-bold text-cyan-400 mb-2">1000+</div>
                            <div class="text-gray-400">完成项目</div>
                        </div>
                    </div>
                </div>

                <!-- 团队介绍 -->
                <div class="scroll-reveal">
                    <h3 class="text-3xl font-bold mb-6 text-tech-gradient">核心团队</h3>
                    <div class="space-y-6">
                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="创始人" class="w-16 h-16 rounded-full mr-4">
                                <div>
                                    <h4 class="text-lg font-bold">张明 - 创始人&CEO</h4>
                                    <p class="text-gray-400">15年影视制作经验，AI技术专家</p>
                                </div>
                            </div>
                            <p class="text-gray-300 text-sm">
                                前知名影视公司技术总监，深度学习领域专家，致力于AI与传统影视制作的融合创新。
                            </p>
                        </div>

                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="技术总监" class="w-16 h-16 rounded-full mr-4">
                                <div>
                                    <h4 class="text-lg font-bold">李雪 - 技术总监</h4>
                                    <p class="text-gray-400">计算机视觉博士，AIGC技术专家</p>
                                </div>
                            </div>
                            <p class="text-gray-300 text-sm">
                                清华大学计算机视觉博士，专注于图像生成和视频处理算法研究，拥有多项AI技术专利。
                            </p>
                        </div>

                        <div class="glass-card p-6 rounded-2xl glow-hover transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="创意总监" class="w-16 h-16 rounded-full mr-4">
                                <div>
                                    <h4 class="text-lg font-bold">王强 - 创意总监</h4>
                                    <p class="text-gray-400">资深导演，视觉创意专家</p>
                                </div>
                            </div>
                            <p class="text-gray-300 text-sm">
                                20年影视创作经验，曾参与多部知名商业广告和影视作品制作，擅长创意策划和视觉设计。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 资质荣誉 -->
            <div class="glass-card p-8 rounded-3xl scroll-reveal">
                <h3 class="text-3xl font-bold mb-8 text-center text-tech-gradient">资质荣誉</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <i class="fas fa-award text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">行业认证</h4>
                        <p class="text-sm text-gray-400">国家高新技术企业</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-trophy text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">获奖荣誉</h4>
                        <p class="text-sm text-gray-400">最佳创新技术奖</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-certificate text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">专业资质</h4>
                        <p class="text-sm text-gray-400">影视制作许可证</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-handshake text-4xl text-cyan-400 mb-4"></i>
                        <h4 class="text-lg font-semibold mb-2">合作伙伴</h4>
                        <p class="text-sm text-gray-400">AI技术联盟成员</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="py-16 px-6 bg-gradient-to-t from-black to-transparent">
        <div class="container mx-auto max-w-7xl">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <!-- 公司信息 -->
                <div>
                    <div class="text-2xl font-bold text-tech-gradient mb-4">U-STUDIO</div>
                    <p class="text-gray-400 mb-6">专业影棚 × AI科技融合 × 无限创意可能</p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
                            <i class="fab fa-weixin text-white"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
                            <i class="fab fa-weibo text-white"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
                            <i class="fab fa-bilibili text-white"></i>
                        </a>
                    </div>
                </div>

                <!-- 快速导航 -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">快速导航</h4>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-cyan-400 transition-colors">首页</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-cyan-400 transition-colors">服务项目</a></li>
                        <li><a href="#studio" class="text-gray-400 hover:text-cyan-400 transition-colors">影棚展示</a></li>
                        <li><a href="#works" class="text-gray-400 hover:text-cyan-400 transition-colors">作品案例</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-cyan-400 transition-colors">关于我们</a></li>
                    </ul>
                </div>

                <!-- 服务项目 -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">服务项目</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">专业拍摄</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">场地租赁</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">AIGC服务</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">融合制作</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-cyan-400 transition-colors">技术支持</a></li>
                    </ul>
                </div>

                <!-- 联系信息 -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <i class="fas fa-phone text-cyan-400 mr-3"></i>
                            <span class="text-gray-400">************</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope text-cyan-400 mr-3"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt text-cyan-400 mr-3"></i>
                            <span class="text-gray-400">北京市朝阳区科技园区</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-clock text-cyan-400 mr-3"></i>
                            <span class="text-gray-400">周一至周日 9:00-22:00</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="border-t border-gray-800 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 U-STUDIO. 保留所有权利. |
                    <a href="#" class="hover:text-cyan-400 transition-colors">隐私政策</a> |
                    <a href="#" class="hover:text-cyan-400 transition-colors">服务条款</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 滚动动画
        function revealOnScroll() {
            const reveals = document.querySelectorAll('.scroll-reveal');

            reveals.forEach(reveal => {
                const windowHeight = window.innerHeight;
                const elementTop = reveal.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < windowHeight - elementVisible) {
                    reveal.classList.add('revealed');
                }
            });
        }

        window.addEventListener('scroll', revealOnScroll);

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(26, 31, 58, 0.95)';
            } else {
                navbar.style.background = 'rgba(26, 31, 58, 0.8)';
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 初始化动画
        document.addEventListener('DOMContentLoaded', function() {
            revealOnScroll();
        });
    </script>

</body>
</html>
